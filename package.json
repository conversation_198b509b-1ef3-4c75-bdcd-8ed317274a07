{"name": "procedure-forms", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "node ./server.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "cross-env NODE_ENV=production node ./server.js", "drizzle:generate": "drizzle-kit generate", "test": "jest", "setup:vscode": "node generate-vscode-settings.js", "setup:env": "cp .env.example .env && echo 'pls edit the .env file'"}, "engines": {"node": ">=20.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "dependencies": {"@alicloud/ocr-api20210707": "^3.1.3", "@alicloud/openapi-core": "^1.0.4", "@epic-web/cachified": "^5.5.1", "@libsql/client": "^0.14.0", "@remix-run/express": "^2.15.2", "@remix-run/node": "^2.15.2", "@remix-run/react": "^2.15.2", "@zxing/library": "^0.21.3", "bcryptjs": "^2.4.3", "compression": "^1.7.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.4", "drizzle-zod": "^0.6.1", "express": "^4.21.2", "express-msgpack": "^5.1.2", "fast-xml-parser": "^5.2.2", "iconv-lite": "^0.6.3", "isbot": "^5.1.19", "lru-cache": "^11.1.0", "minio": "^8.0.3", "morgan": "^1.10.0", "msgpackr": "^1.11.2", "pg": "^8.13.1", "react": "^18.3.1", "react-dom": "^18.3.1", "remix-auth": "^3.7.0", "remix-auth-form": "^1.5.0", "sharp": "^0.34.2", "slug": "^10.0.0", "tesseract.js": "^6.0.1", "tiny-invariant": "^1.3.3", "type-fest": "^4.31.0", "zod": "^3.24.1"}, "devDependencies": {"@remix-run/dev": "^2.15.2", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/pg": "^8.11.10", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/slug": "^5.0.9", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.2", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "jest": "^29.7.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.7.3", "vite": "^6.0.7", "vite-tsconfig-paths": "^5.1.4"}}