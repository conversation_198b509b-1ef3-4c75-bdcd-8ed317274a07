import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { and, eq } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  material_allocats as model,
  material_allocat_zi,
  material_allocat_zu,
  type MaterialAllocat,
  type NewMaterialAllocat,
  type UpdateMaterialAllocat,
} from "~/schema/material_allocats"
import * as schema from "~/schema/material_allocats"
import { points } from "~/schema/points"
import { forms } from "~/schema/forms"
import { materials } from "~/schema/materials"

const db = drizzle(process.env.DATABASE_URL!, { schema })

export const find = async (
  project_id?: number,
  material_id?: number,
  point_id?: number,
  form_id?: number,
  offset?: number,
  limit?: number,
) =>
  await db.query.material_allocats.findMany({
    where: and(
      project_id ? eq(model.project_id, project_id) : undefined,
      material_id ? eq(model.material_id, material_id) : undefined,
      point_id ? eq(model.point_id, point_id) : undefined,
      form_id ? eq(model.form_id, form_id) : undefined,
    ),
    limit,
    offset,
  })

export async function insert(data: NewMaterialAllocat): Promise<MaterialAllocat> {
  const { success, error } = material_allocat_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_allocat(data.project_id, data.material_id, data.point_id, data.form_id)

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function insert_batch(data_batch: NewMaterialAllocat[]): Promise<MaterialAllocat[]> {
  for (const data of data_batch) {
    const { success, error } = material_allocat_zi.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const result: MaterialAllocat[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      if (!(await restrict_allocat(data.project_id, data.material_id, data.point_id, data.form_id))) continue

      const allocat = await tx.insert(model).values(data).returning()
      result.push(allocat[0])
    }
  })
  return result
}

export async function update(data: UpdateMaterialAllocat): Promise<MaterialAllocat> {
  const { success, error } = material_allocat_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  // const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  // if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { material_id, project_id, point_id, form_id, ...updated_data } = data
  await restrict_allocat(project_id, material_id, point_id, form_id)

  const result = await db
    .update(model)
    .set(updated_data)
    .where(
      and(
        project_id ? eq(model.project_id, project_id) : undefined,
        material_id ? eq(model.material_id, material_id) : undefined,
        point_id ? eq(model.point_id, point_id) : undefined,
        form_id ? eq(model.form_id, form_id) : undefined,
      ),
    )
    .returning()
  return result[0]
}

export async function update_batch(data_batch: UpdateMaterialAllocat[]): Promise<MaterialAllocat[]> {
  for (const data of data_batch) {
    const { success, error } = material_allocat_zu.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const result: MaterialAllocat[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      const { material_id, project_id, point_id, form_id, ...updated_data } = data
      if (!(await restrict_allocat(project_id, material_id, point_id, form_id))) continue

      const allocat_r = await tx
        .update(model)
        .set(updated_data)
        .where(
          and(
            project_id ? eq(model.project_id, project_id) : undefined,
            material_id ? eq(model.material_id, material_id) : undefined,
            point_id ? eq(model.point_id, point_id) : undefined,
            form_id ? eq(model.form_id, form_id) : undefined,
          ),
        )
        .returning()
      result.push(allocat_r[0])
    }
  })
  return result
}

export async function remove(data: MaterialAllocat): Promise<void> {
  await db
    .delete(model)
    .where(
      and(
        eq(model.project_id, data.project_id),
        eq(model.material_id, data.material_id),
        eq(model.point_id, data.point_id),
        eq(model.form_id, data.form_id),
      ),
    )
}

export async function remove_batch(data_batch: MaterialAllocat[]): Promise<void> {
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      await tx
        .delete(model)
        .where(
          and(
            eq(model.project_id, data.project_id),
            eq(model.material_id, data.material_id),
            eq(model.point_id, data.point_id),
            eq(model.form_id, data.form_id),
          ),
        )
    }
  })
}

export const restrict_allocat = async (
  project_id?: number,
  material_id?: number,
  point_id?: number,
  form_id?: number,
  preserved?: MaterialAllocat,
  duplicated_safe: boolean = false,
): Promise<boolean> => {
  // if (preserved) {
  //   if (project_id && project_id !== preserved.project_id)
  //     throw new Response(`project_id not allowed change ${project_id}`, {
  //       status: Status.Conflict,
  //     })

  //   if (
  //     !(material_id && material_id !== preserved.material_id) &&
  //     !(point_id && point_id !== preserved.point_id) &&
  //     !(form_id && form_id !== preserved.form_id)
  //   ) {
  //     return true
  //   }

  //   project_id = project_id ?? preserved.project_id
  //   material_id = material_id ?? preserved.material_id
  //   point_id = point_id ?? preserved.point_id
  //   form_id = form_id ?? preserved.form_id
  // }

  // const duplicated_result = await db
  //   .select()
  //   .from(model)
  //   .where(
  //     and(
  //       eq(model.project_id, project_id!),
  //       eq(model.material_id, material_id!),
  //       eq(model.point_id, point_id!),
  //       eq(model.form_id, form_id!),
  //     ),
  //   )

  // if (duplicated_result.length > 0) {
  //   if (duplicated_safe) return false
  //   throw new Response(`restrict_allocat duplicated ${project_id} ${material_id} ${point_id} ${form_id}`, {
  //     status: Status.Conflict,
  //   })
  // }

  const material_result = await db.select().from(materials).where(eq(materials.id, material_id!))
  if (!material_result.length) {
    throw new Response(`restrict_allocat material null ${material_id}`, { status: Status.BadRequest })
  } else if (material_result[0].project_id !== project_id) {
    throw new Response(`restrict_allocat material project conflict ${material_id}`, {
      status: Status.Conflict,
    })
  }

  const point_result = await db.select().from(points).where(eq(points.id, point_id!))
  if (!point_result.length) {
    throw new Response(`restrict_allocat point null ${point_id}`, { status: Status.BadRequest })
  } else if (point_result[0].project_id !== project_id) {
    throw new Response(`restrict_allocat point project conflict ${point_id}`, { status: Status.Conflict })
  }

  const form_result = await db.select().from(forms).where(eq(forms.id, form_id!))
  if (!point_result.length) {
    throw new Response(`restrict_allocat form null ${form_id}`, { status: Status.BadRequest })
  } else if (form_result[0].project_id !== project_id) {
    throw new Response(`restrict_allocat form project conflict ${form_id}`, { status: Status.Conflict })
  }

  return true
}
