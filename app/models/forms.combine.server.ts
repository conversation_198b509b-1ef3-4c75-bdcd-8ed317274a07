import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, gte, lte } from "drizzle-orm"
import { HttpStatusCode as Status } from "~/utils/http_code"
import slug from "slug"

import * as forms_schema from "~/schema/forms"
import * as submission_schema from "~/schema/submissions"
import * as units_schema from "~/schema/units"
import * as projects_schema from "~/schema/projects"

import { forms } from "~/schema/forms"
import {
  form_combine_zi,
  form_combine_zu,
  type UpdateFormCombine,
  type FormCombine,
  type NewFormCombine,
} from "~/schema/forms.combine"
import { units, type Unit } from "~/schema/units"
import { submissions } from "~/schema/submissions"
import { restrict_forms_unique } from "./forms.server"
import { restrict_units_slug_unique } from "./units.server"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...submission_schema, ...units_schema, ...projects_schema, ...forms_schema },
})

export async function get(
  id: FormCombine["id"],
  update_time_start?: string,
  update_time_end?: string,
  plan_time_start?: string,
  plan_time_end?: string,
): Promise<FormCombine | null> {
  const result = await db.query.forms.findMany({
    where: eq(forms.id, id),
    with: {
      units: true,
      project: true,
      // submissions: true,
    },
  })

  if (result.length === 0) return null

  const submissions_ = await db.query.submissions.findMany({
    where: and(
      eq(submissions.form_id, id),
      update_time_start ? gte(submissions.update_time, update_time_start) : undefined,
      update_time_end ? lte(submissions.update_time, update_time_end) : undefined,
      plan_time_start ? gte(submissions.plan_time, plan_time_start) : undefined,
      plan_time_end ? lte(submissions.plan_time, plan_time_end) : undefined,
    ),
  })

  return {
    ...result[0],
    submitings: submissions_,
  }
}

export async function find(project_id: number, name: string | undefined): Promise<FormCombine[]> {
  const result = await db.query.forms.findMany({
    where: and(eq(forms.project_id, project_id), name ? eq(forms.name, name) : undefined),
    with: {
      units: true,
    },
  })

  return result
}

export async function insert(data: NewFormCombine): Promise<FormCombine | null> {
  if (!data.slug) {
    data.slug = slug(data.name)
  }

  const { success, error } = form_combine_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_forms_unique(data.project_id, data.slug)

  let combine: FormCombine | null = null
  await db.transaction(async (tx) => {
    const preserved_ = await tx.insert(forms).values(data).returning()
    combine = { ...preserved_[0], units: [] }

    for (const unit of data.units) {
      await restrict_units_slug_unique(unit.form_id, unit.slug)

      const preserved_ = await tx
        .insert(units)
        .values({ ...unit, form_id: combine.id })
        .returning()
      combine.units.push(preserved_[0])
    }
  })

  return combine
}

export async function update(data: UpdateFormCombine): Promise<FormCombine | null> {
  const { success, error } = form_combine_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(forms).where(eq(forms.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  await restrict_forms_unique(data.project_id, data.slug, preserved_[0])

  let result: FormCombine | null = null
  await db.transaction(async (tx) => {
    const { id: form_id, ...updated_data } = data
    const form_updated = await tx.update(forms).set(updated_data).where(eq(forms.id, form_id)).returning()
    result = { ...form_updated[0], units: [] }

    for (const unit of data.units) {
      let unit_result: Unit[] | null = null
      if ("id" in unit) {
        const preserved_ = await tx.select().from(units).where(eq(units.id, unit.id!))
        if (preserved_.length === 0) throw new Response(`unit not found ${unit}`, { status: Status.NotFound })

        await restrict_units_slug_unique(unit.form_id, unit.slug, preserved_[0])

        const { id, ...unit_updating } = unit
        unit_result = await tx.update(units).set(unit_updating).where(eq(units.id, id!)).returning()
      } else {
        unit.form_id = form_id
        await restrict_units_slug_unique(unit.form_id, unit.slug)
        unit_result = await tx.insert(units).values(unit).returning()
      }
      result.units.push(unit_result[0])
    }
  })

  return result
}
