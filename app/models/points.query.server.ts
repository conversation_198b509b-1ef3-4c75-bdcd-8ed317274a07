import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, inArray, sql, asc, desc } from "drizzle-orm"

import * as submission_schema from "~/schema/submissions"
import * as points_schema from "~/schema/points"
import * as forms_schema from "~/schema/forms"
import * as units_schema from "~/schema/units"

import { forms } from "~/schema/forms"
import { submissions } from "~/schema/submissions"
import { points as model } from "~/schema/points"
import { type PointType } from "~/schema/point_types"
import { in_integer_array, eq_optional_integer, eq_optional_point_types } from "~/utils/sql"
import { Order, OrderScending } from "~/utils/params_parse"
import { PointForQueryWithValues } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...submission_schema, ...points_schema, ...forms_schema, ...units_schema },
  // logger: true,
})

export const find = async (
  project_id: number,
  point_type?: PointType,
  point_id?: number,
  offset?: number,
  limit?: number,
  // orders: Order[],
): Promise<PointForQueryWithValues[]> => {
  const points_result = await db.query.points.findMany({
    columns: {
      id: true,
      // name: true,
      slug: true,
      type: true,
      status: true,
    },
    where: and(
      eq(model.project_id, project_id),
      point_id ? eq(model.id, point_id) : undefined,
      point_type ? eq(model.type, point_type) : undefined,
    ),
    with: {
      parent: {
        columns: {
          // name: true,
          slug: true,
        },
      },
    },
  })
  const point_ids = points_result.map((p) => p.id)

  const forms_result = await db.query.forms.findMany({
    columns: {
      id: true,
    },
    where: and(eq(forms.project_id, project_id), eq(forms.slug, "baseForm")),
  })
  const form_ids = forms_result.map((f) => f.id)

  const submission_result = await db.query.submissions.findMany({
    columns: {
      values: true,
      point_id: true,
    },
    where: and(inArray(submissions.form_id, form_ids), inArray(submissions.point_id, point_ids)),
    // orderBy: orders.map((o) =>
    //   o.scending === OrderScending.ASC ? asc(sql`${o.column}`) : desc(sql`${o.column}`),
    // ),
    offset,
    limit,
  })

  return submission_result.map((submission) => {
    const point = points_result.find((p) => p.id === submission.point_id)!
    return {
      ...point,
      parent: point.parent ?? undefined,
      values: submission.values ?? undefined,
    }
  })
}

const points_prepare = db.query.points
  .findMany({
    columns: {
      id: true,
      // name: true,
      slug: true,
      type: true,
      status: true,
      parent_id: true,
    },
    where: and(
      eq(model.project_id, sql.placeholder("project_id")),
      eq_optional_integer(model.id, "point_id"),
      eq_optional_point_types(model.type, "point_type"),
    ),
    // with: {
    //   parent: {
    //     columns: {
    //       // name: true,
    //       slug: true,
    //     },
    //   },
    // },
  })
  .prepare("points_query_points")

const forms_prepare = db.query.forms
  .findMany({
    columns: {
      id: true,
    },
    where: and(eq(forms.project_id, sql.placeholder("project_id")), eq(forms.slug, "baseForm")),
  })
  .prepare("points_query_forms")

const submission_prepare = db.query.submissions
  .findMany({
    columns: {
      values: true,
      point_id: true,
    },
    where: and(
      in_integer_array(submissions.point_id, "point_ids"),
      in_integer_array(submissions.form_id, "form_ids"),
    ),
    // orderBy: sql`${sql.placeholder("orders_sql")}`,
    offset: sql.placeholder("offset"),
    limit: sql.placeholder("limit"),
  })
  .prepare("points_query_submission")

export async function find_prepare(
  project_id: number,
  point_type: PointType | undefined,
  point_id: number | undefined,
  offset: number | undefined,
  limit: number | undefined,
  // orders: Order[],
): Promise<PointForQueryWithValues[]> {
  const points_result = await points_prepare.execute({
    project_id,
    point_id,
    point_type,
  })
  const forms_result = await forms_prepare.execute({ project_id })
  // const orders_sql = orders.map((o) => `${o.column} ${o.scending}`).join(", ")
  const submission_result = await submission_prepare.execute({
    point_ids: points_result.map((r) => r.id).join(","),
    form_ids: forms_result.map((r) => r.id).join(","),
    // orders_sql,
    offset,
    limit,
  })

  return submission_result.map((submission) => {
    const point = points_result.find((p) => p.id === submission.point_id)!
    return {
      ...point,
      // parent: point.parent ?? undefined,
      values: submission.values ?? undefined,
    }
  })
}
