import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as projects_schema from "~/schema/projects"
import * as config_cates_schema from "~/schema/configs"

import {
  configs as model,
  config_zi,
  config_zu,
  type Config,
  type NewConfig,
  type UpdateConfig,
} from "~/schema/configs"

const db = drizzle(process.env.DATABASE_URL!, { schema: { ...config_cates_schema, ...projects_schema } })

export const get = async (id: Config["id"]): Promise<Config | undefined> =>
  await db.query.configs.findFirst({
    where: eq(model.id, id),
    with: {
      parent: true,
    },
  })

export async function find(cate_id?: number, code?: string, name?: string) {
  const result = await db.query.configs.findMany({
    where: and(
      cate_id ? eq(model.cate_id, cate_id) : undefined,
      code ? ilike(model.code, code) : undefined,
      name ? ilike(model.name, `%${name}%`) : undefined,
    ),
    with: {
      parent: {
        with: {
          parent: true,
        },
      },
    },
  })

  return result
}

export async function insert(data: NewConfig): Promise<Config> {
  const { success, error } = config_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_configs_unique(data.cate_id, data.code)

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function insert_batch(data_batch: NewConfig[]): Promise<Config[]> {
  for (const data of data_batch) {
    const { success, error } = config_zi.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const result: Config[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      await restrict_configs_unique(data.cate_id, data.code)

      const result_ = await tx.insert(model).values(data).returning()
      result.push(result_[0])
    }
  })
  return result
}

export async function update(data: UpdateConfig): Promise<Config> {
  const { success, error } = config_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  await restrict_configs_unique(data.cate_id, data.code, preserved_[0])

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: Config["id"]) {
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_configs_unique = async (cate_id?: number, code?: string, preserved?: Config) => {
  if (preserved) {
    if (!(cate_id && cate_id !== preserved.cate_id) && !(code && code !== preserved.code)) {
      return
    }

    cate_id = cate_id ?? preserved.cate_id
    code = code ?? preserved.code
  }

  const result = await db
    .select()
    .from(model)
    .where(and(eq(model.cate_id, cate_id!), eq(model.code, code!)))
  if (result.length > 0) {
    throw new Response(`restrict_configs_unique ${code}`, { status: Status.Conflict })
  }
}
