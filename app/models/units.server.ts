import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"
import slug from "slug"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { units as model, unit_zi, unit_zu, type Unit, type NewUnit, UpdateUnit } from "~/schema/units"
import { submissions } from "~/schema/submissions"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Unit["id"]): Promise<Unit | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (form_id?: number) =>
  await db
    .select()
    .from(model)
    .where(form_id ? eq(model.form_id, form_id) : undefined)

export async function insert(data: NewUnit): Promise<Unit> {
  if (!data.slug) {
    data.slug = slug(`${data.index}_${data.label}`)
  }

  const { success, error } = unit_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_units_slug_unique(data.form_id, data.slug)

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateUnit) {
  const { success, error } = unit_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  await restrict_units_slug_unique(data.form_id, data.slug, preserved_[0])

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: Unit["id"]) {
  const unit = await get(id)
  if (!unit) {
    throw new Response("not found", { status: Status.NotFound })
  }

  const submissions_ = await db.select().from(submissions).where(eq(submissions.form_id, unit.form_id))
  for (const submission of submissions_) {
    if (submission.hasOwnProperty(unit.slug)) {
      throw new Response("unit relation submission value", { status: Status.Conflict })
    }
  }
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_units_slug_unique = async (form_id?: number, slug?: string, preserved?: Unit) => {
  if (preserved) {
    if (!(form_id && form_id !== preserved.form_id) && !(slug && slug !== preserved.slug)) {
      return
    }

    form_id = form_id ?? preserved.form_id
    slug = slug ?? preserved.slug
  }

  const result = await db
    .select()
    .from(model)
    .where(and(eq(model.form_id, form_id!), eq(model.slug, slug!)))

  if (result.length > 0) {
    throw new Response(`restrict_units_slug_unique ${form_id} ${slug}`, { status: Status.Conflict })
  }
}

export const restrict_unit_belong_form = async (unit_id: number, form_id: number) => {
  const units_result = await db
    .select()
    .from(model)
    .where(and(eq(model.id, unit_id), eq(model.form_id, form_id)))

  if (units_result.length === 0) {
    throw new Response(`restrict_unit_belong_form ${form_id} ${unit_id}`, {
      status: Status.BadRequest,
    })
  }
}
