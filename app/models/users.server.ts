import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ne, ilike, and } from "drizzle-orm"
import { HttpStatusCode as Status } from "~/utils/http_code"
import { storage_copy } from "~/storage.server"

import { users as model, user_za, user_zu, type User, type AddUser, UpdateUser } from "~/schema/users"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: User["id"]): Promise<User | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export async function find(
  name_chinese?: string,
  account_id?: number,
  attendance?: boolean,
): Promise<User[]> {
  const result = await db
    .select()
    .from(model)
    .where(
      and(
        name_chinese ? ilike(model.name_chinese, `%${name_chinese}%`) : undefined,
        account_id ? eq(model.account_id, account_id) : undefined,
        attendance != undefined ? eq(model.attendance, attendance) : undefined,
      ),
    )

  return result
}

export async function insert(data: AddUser): Promise<User> {
  const { success, error } = user_za.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [inserted] = await db.insert(model).values(data).returning()

  if (data.using) {
    await db
      .update(model)
      .set({ using: false })
      .where(and(eq(model.account_id, data.account_id), ne(model.id, inserted.id)))
  }

  return inserted
}

export const update = async (data: UpdateUser): Promise<User> => {
  const { success, error } = user_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()

  if (data.account_id && data.using) {
    await db
      .update(model)
      .set({ using: false })
      .where(and(eq(model.account_id, data.account_id), ne(model.id, updated.id)))
  }

  return updated
}

export const update_using = async (id: User["id"]): Promise<User> => {
  const user = await get(id)
  if (!user || !user.account_id) throw new Response("not found", { status: Status.NotFound })

  return await db.transaction(async (tx) => {
    if (!user.account_id) throw new Response("missing account_id", { status: Status.BadRequest })

    await tx.update(model).set({ using: false }).where(eq(model.account_id, user.account_id))
    const [updated] = await tx.update(model).set({ using: true }).where(eq(model.id, id)).returning()
    return updated
  })
}

export async function sync(id: User["id"]) {
  const user = await get(id)
  if (!user || !user.account_id) throw new Response("not found account", { status: Status.NotFound })

  const users = await db
    .update(model)
    .set({
      name_chinese: user.name_chinese,
      role: user.role,
      phone: user.phone,
      identity: user.identity,
      bankcard: user.bankcard,
      gender: user.gender,
      attendance: user.attendance,
    })
    .where(eq(model.account_id, user.account_id))
    .returning()

  for (const u of users) {
    if (u.id === id) continue
    await storage_copy("users", `${id}.jpg`, `${u.id}.jpg`, false)
  }

  return users
}
