import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { and, eq, ilike, asc, desc, count } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  materials as model,
  material_zi,
  material_zu,
  type Material,
  type NewMaterial,
  type UpdateMaterial,
} from "~/schema/materials"
import * as schema from "~/schema/materials"
import { stat as object_stat } from "~/storage.server"

const db = drizzle(process.env.DATABASE_URL!, { schema })

export const get = async (id: Material["id"]): Promise<Material | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const get_count = async (project_id: number) =>
  await db
    .select({ count: count() })
    .from(model)
    .where(eq(model.project_id, project_id))
    .then((rows) => rows.at(0))

export const find = async (
  project_id: number,
  code?: string,
  name?: string,
  offset?: number,
  limit?: number,
  image_stat = false,
): Promise<Material[]> => {
  const result: Material[] = await db.query.materials.findMany({
    where: and(
      eq(model.project_id, project_id),
      code ? ilike(model.code, code) : undefined,
      name ? ilike(model.name, `%${name}%`) : undefined,
    ),
    orderBy: [desc(model.create_time)],
    limit,
    offset,
  })

  if (image_stat) {
    await Promise.all(
      result.map(async (item) => {
        item.front = await object_stat("material-images", `${project_id}/${item.id}/front.jpg`)
        item.opposite = await object_stat("material-images", `${project_id}/${item.id}/opposite.jpg`)
      }),
    )
  }

  return result
}

export const insert = async (data: NewMaterial): Promise<Material> => {
  const { success, error } = material_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_materials_code_unique(data.project_id, data.code)

  const [inserted] = await db.insert(model).values(data).returning()
  return inserted
}

export const update = async (data: UpdateMaterial): Promise<Material> => {
  const { success, error } = material_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [preserved] = await db.select().from(model).where(eq(model.id, data.id))
  if (!preserved) throw new Response("not found", { status: Status.NotFound })

  await restrict_materials_code_unique(data.project_id, data.code, preserved)

  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return updated
}

export const remove = async (id: Material["id"]): Promise<void> => {
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_materials_code_unique = async (
  project_id?: number,
  code?: string,
  preserved?: Material,
): Promise<void> => {
  if (preserved) {
    const project_changed = project_id && project_id !== preserved.project_id
    const code_changed = code && code !== preserved.code

    if (!project_changed && !code_changed) {
      return
    }

    project_id = project_id ?? preserved.project_id
    code = code ?? preserved.code
  }

  if (!project_id || !code) return

  const [existing] = await db
    .select()
    .from(model)
    .where(and(eq(model.project_id, project_id), eq(model.code, code)))

  if (existing) {
    throw new Response(`restrict_materials_code_unique ${code}`, { status: Status.Conflict })
  }
}
