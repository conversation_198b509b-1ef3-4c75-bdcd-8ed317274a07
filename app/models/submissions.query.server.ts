import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, sql, asc, desc } from "drizzle-orm"

import * as submission_schema from "~/schema/submissions"
import * as forms_schema from "~/schema/forms"
import * as units_schema from "~/schema/units"
import * as points_schema from "~/schema/points"
import * as users_schema from "~/schema/users"

import { submissions as model } from "~/schema/submissions"
// import { Order, OrderScending } from "~/utils/params_parse"
import { eq_optional_boolean, eq_optional_integer } from "~/utils/sql"
import type { SubmissionForQueryWith } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...submission_schema, ...forms_schema, ...units_schema, ...points_schema, ...users_schema },
})

export const find = async (
  form_id?: number,
  point_id?: number,
  user_id?: number,
  material_id?: number,
  done?: boolean,
  offset?: number,
  limit?: number,
  // orders: Order[],
): Promise<SubmissionForQueryWith[]> => {
  const result = await db.query.submissions.findMany({
    columns: {
      id: true,
      point_id: true,
      form_id: true,
      material_id: true,
      cabinet_material_id: true,
      values: true,
      status: true,
      done: true,
      done_user: true,
      orgs: true,
      user_ids: true,
      plan_time: true,
    },
    where: and(
      form_id ? eq(model.form_id, form_id) : undefined,
      point_id ? eq(model.point_id, point_id) : undefined,
      user_id ? eq(model.user_id, user_id) : undefined,
      material_id ? eq(model.material_id, material_id) : undefined,
      done !== undefined ? eq(model.done, done) : undefined,
    ),
    // orderBy: orders.map((o) =>
    //   o.scending === OrderScending.ASC ? asc(sql`${o.column}`) : desc(sql`${o.column}`),
    // ),
    offset,
    limit,
    with: {
      point: {
        columns: {
          id: true,
          name: true,
          slug: true,
          status: true,
          type: true,
        },
        with: {
          parent: {
            columns: {
              id: true,
              name: true,
              slug: true,
              type: true,
              status: true,
            },
          },
        },
      },
    },
  })

  return result
}

const submission_prepare = db.query.submissions
  .findMany({
    columns: {
      id: true,
      values: true,
      point_id: true,
      form_id: true,
      material_id: true,
      cabinet_material_id: true,
      status: true,
      done: true,
      done_user: true,
      orgs: true,
      user_ids: true,
      plan_time: true,
    },
    where: and(
      eq_optional_integer(model.form_id, "form_id"),
      eq_optional_integer(model.point_id, "point_id"),
      eq_optional_integer(model.user_id, "user_id"),
      eq_optional_integer(model.material_id, "material_id"),
      eq_optional_integer(model.cabinet_material_id, "cabinet_material_id"),
      eq_optional_boolean(model.done, "done"),
    ),
    // orderBy: sql`${sql.placeholder("orders_sql")}`,
    offset: sql.placeholder("offset"),
    limit: sql.placeholder("limit"),
    with: {
      point: {
        columns: {
          id: true,
          name: true,
          slug: true,
          type: true,
          status: true,
        },
        with: {
          parent: {
            columns: {
              id: true,
              name: true,
              slug: true,
              type: true,
              status: true,
            },
          },
        },
      },
    },
  })
  .prepare("submission_query")

export async function find_prepare(
  form_id: number | undefined,
  point_id: number | undefined,
  user_id: number | undefined,
  material_id: number | undefined,
  cabinet_material_id: number | undefined,
  done: boolean | undefined,
  offset: number | undefined,
  limit: number | undefined,
  // orders: Order[],
): Promise<SubmissionForQueryWith[]> {
  // const orders_sql = orders.map((o) => `${o.column} ${o.scending}`).join(", ")
  const result = await submission_prepare.execute({
    form_id,
    point_id,
    user_id,
    material_id,
    cabinet_material_id,
    done,
    offset,
    limit,
    // orders_sql,
  })

  return result
}
