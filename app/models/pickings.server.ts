import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  pickings as model,
  picking_zi,
  picking_zu,
  type Picking,
  type PickingWith,
  type UpdatePickingWith,
  type DocumentType,
} from "~/schema/pickings"
import { picking_items, type NewPickingItem, type UpdatePickingItem } from "~/schema/picking_items"
import * as users_schema from "~/schema/users"
import * as pickings_schema from "~/schema/pickings"
import * as picking_items_schema from "~/schema/picking_items"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: {
    ...pickings_schema,
    ...picking_items_schema,
    ...users_schema,
  },
  // logger: true,
})

export const get = async (id: Picking["id"]): Promise<Picking | undefined> =>
  await db.query.pickings.findFirst({
    where: eq(model.id, id),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      receiver: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      items: true,
    },
  })

export const find = async (
  project_id: number,
  creator_id?: number,
  receiver_id?: number,
  type?: DocumentType,
  with_items = false,
  offset?: number,
  limit?: number,
): Promise<Picking[]> =>
  await db.query.pickings.findMany({
    where: and(
      eq(model.project_id, project_id),
      creator_id ? eq(model.creator_id, creator_id) : undefined,
      receiver_id ? eq(model.receiver_id, receiver_id) : undefined,
      type ? eq(model.type, type) : undefined,
    ),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      receiver: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      items: with_items ? true : undefined,
    },
    limit,
    offset,
  })

export async function insert(data: PickingWith): Promise<PickingWith> {
  const { items, ...picking } = data
  const { success, error } = picking_zi.safeParse(picking)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  return await db.transaction<PickingWith>(async (tx) => {
    const picking_ = await tx.insert(model).values(picking).returning()
    const result: PickingWith = { ...picking_[0], items: [] }

    for (const item of items) {
      const item_ = await tx
        .insert(picking_items)
        .values({ ...item, picking_id: result.id })
        .returning()
      result.items.push(item_[0])
    }

    return result
  })
}

export async function update(data: UpdatePickingWith): Promise<PickingWith> {
  const { items, ...picking } = data
  const { success, error } = picking_zu.safeParse(picking)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  return await db.transaction<PickingWith>(async (tx) => {
    const { id, ...updated_data } = picking
    const picking_ = await tx.update(model).set(updated_data).where(eq(model.id, id)).returning()
    const result: PickingWith = { ...picking_[0], items: [] }

    for (const item of items) {
      if (item.id) {
        const { id, ...updated_data } = item as UpdatePickingItem
        const item_ = await tx
          .update(picking_items)
          .set(updated_data)
          .where(eq(picking_items.id, item.id))
          .returning()
        result.items.push(item_[0])
      } else {
        const item_ = await tx
          .insert(picking_items)
          .values({ ...(item as NewPickingItem), picking_id: result.id })
          .returning()
        result.items.push(item_[0])
      }
    }

    return result
  })
}

export async function remove(id: Picking["id"]) {
  await db.delete(model).where(eq(model.id, id))
}

export async function removeItem(id: number) {
  await db.delete(picking_items).where(eq(picking_items.id, id))
}
