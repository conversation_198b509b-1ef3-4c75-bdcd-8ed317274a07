import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import slug from "slug"
import { eq, ilike, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { forms as model, form_zi, form_zu, type Form, type NewForm, type UpdateForm } from "~/schema/forms"
import { submissions } from "~/schema/submissions"
import { sanitizeLike } from "~/utils/sanitize_input"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Form["id"]): Promise<Form | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (project_id: number, name?: string) =>
  await db
    .select()
    .from(model)
    .where(
      and(eq(model.project_id, project_id), name ? ilike(model.name, `%${sanitizeLike(name)}%`) : undefined),
    )

export const insert = async (data: NewForm): Promise<Form> => {
  if (!data.slug) {
    data.slug = slug(data.name)
  }

  const { success, error } = form_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_forms_unique(data.project_id, data.slug)

  const [inserted] = await db.insert(model).values(data).returning()
  return inserted
}

export const update = async (data: UpdateForm): Promise<Form> => {
  const { success, error } = form_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [preserved] = await db.select().from(model).where(eq(model.id, data.id))
  if (!preserved) throw new Response("not found", { status: Status.NotFound })

  await restrict_forms_unique(data.project_id, data.slug, preserved)

  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return updated
}

export const remove = async (id: Form["id"]): Promise<void> => {
  const [existing] = await db.select().from(submissions).where(eq(submissions.form_id, id))
  if (existing) {
    throw new Response("has submissions", { status: Status.Conflict })
  }

  await db.delete(model).where(eq(model.id, id))
}

export const restrict_forms_unique = async (
  project_id?: number,
  slug?: string,
  preserved?: Form,
): Promise<void> => {
  if (preserved) {
    const project_changed = project_id && project_id !== preserved.project_id
    const slug_changed = slug && slug !== preserved.slug

    if (!project_changed && !slug_changed) {
      return
    }

    project_id = project_id ?? preserved.project_id
    slug = slug ?? preserved.slug
  }

  if (!project_id || !slug) return

  const [existing] = await db
    .select()
    .from(model)
    .where(and(eq(model.project_id, project_id), eq(model.slug, slug)))

  if (existing) {
    throw new Response(`restrict_forms_unique ${slug}`, { status: Status.Conflict })
  }
}
