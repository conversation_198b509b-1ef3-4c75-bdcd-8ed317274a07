import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, inArray, gte, lte, desc, count } from "drizzle-orm"
import { type PgSelectBuilder } from "drizzle-orm/pg-core"

import {
  submissions as model,
  submission_zi,
  submission_zu,
  type Submission,
  type NewSubmission,
  type SubmissionStatus,
  UpdateSubmission,
} from "~/schema/submissions"
import { forms } from "~/schema/forms"
import { HttpStatusCode as Status } from "~/utils/http_code"
import dayjs from "dayjs"
import { type User } from "~/schema/users"
import { submission_logs } from "~/schema/submission_logs"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Submission["id"]): Promise<Submission | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const get_count = async (
  form_ids: number[],
  project_id?: number,
  point_id?: number,
  user_id?: number,
  material_id?: number,
  cabinet_material_id?: number,
  done?: boolean,
  done_user_id?: number,
  statuses?: SubmissionStatus[],
  update_time_start?: string,
  update_time_end?: string,
) =>
  await db
    .select({ count: count() })
    .from(model)
    .where(
      and(
        form_ids.length ? inArray(model.form_id, form_ids) : undefined,
        project_id ? eq(model.project_id, project_id) : undefined,
        point_id ? eq(model.point_id, point_id) : undefined,
        user_id ? eq(model.user_id, user_id) : undefined,
        material_id ? eq(model.material_id, material_id) : undefined,
        cabinet_material_id ? eq(model.cabinet_material_id, cabinet_material_id) : undefined,
        done !== undefined ? eq(model.done, done) : undefined,
        done_user_id ? eq(model.done_user, done_user_id) : undefined,
        statuses && statuses.length ? inArray(model.status, statuses) : undefined,
        update_time_start ? gte(model.update_time, update_time_start) : undefined,
        update_time_end ? lte(model.update_time, update_time_end) : undefined,
      ),
    )
    .then((rows) => rows.at(0))

export async function find(
  form_ids: number[],
  project_id?: number,
  point_id?: number,
  user_id?: number,
  material_id?: number,
  cabinet_material_id?: number,
  done?: boolean,
  done_user_id?: number,
  statuses?: SubmissionStatus[],
  update_time_start?: string,
  update_time_end?: string,
  plan_time_start?: string,
  plan_time_end?: string,
) {
  return await db
    .select()
    .from(model)
    .where(
      and(
        form_ids.length ? inArray(model.form_id, form_ids) : undefined,
        project_id ? eq(model.project_id, project_id) : undefined,
        point_id ? eq(model.point_id, point_id) : undefined,
        user_id ? eq(model.user_id, user_id) : undefined,
        material_id ? eq(model.material_id, material_id) : undefined,
        cabinet_material_id ? eq(model.cabinet_material_id, cabinet_material_id) : undefined,
        update_time_start ? gte(model.update_time, update_time_start) : undefined,
        update_time_end ? lte(model.update_time, update_time_end) : undefined,
        plan_time_start ? gte(model.plan_time, plan_time_start) : undefined,
        plan_time_end ? lte(model.plan_time, plan_time_end) : undefined,
        done !== undefined ? eq(model.done, done) : undefined,
        done_user_id ? eq(model.done_user, done_user_id) : undefined,
        statuses && statuses.length ? inArray(model.status, statuses) : undefined,
      ),
    )
    .orderBy(desc(model.update_time))
}

export async function getLogs(submission_id: number) {
  const result = await db
    .select()
    .from(submission_logs)
    .where(eq(submission_logs.submiting_id, submission_id))
  return result
}

export async function insert(data: NewSubmission): Promise<Submission> {
  const { success, error } = submission_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  return await db.transaction(async (tx) => {
    await restrict_submission_singleton(tx.select(), data.form_id, data.point_id)

    const inserted_ = await tx.insert(model).values(data).returning()
    return inserted_[0]
  })
}

export async function insert_batch(data_batch: NewSubmission[]): Promise<Submission[]> {
  for (const data of data_batch) {
    const { success, error } = submission_zi.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const inserts: Submission[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      await restrict_submission_singleton(tx.select(), data.form_id, data.point_id)

      const inserted_ = await tx.insert(model).values(data).returning()
      inserts.push(inserted_[0])
    }
  })

  return inserts
}

export async function update(data: UpdateSubmission, submitter: User): Promise<Submission> {
  const { success, error } = submission_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })
  const preserved = preserved_[0]

  if (preserved.form_id !== data.form_id || preserved.point_id !== data.point_id)
    throw new Response("form_id, point_id not allowed change", { status: Status.BadRequest })

  const submitter_name = submitter.name_chinese || `user_${submitter.id}`
  const { id: submission_id, ...preserved_log } = preserved
  if (!(await getLogs(submission_id)).length) {
    await db.insert(submission_logs).values({
      ...preserved_log,
      submiting_id: submission_id,
      submitter_name,
    })
  }

  if (data.done && !preserved.done) {
    data.done_user = data.user_id
  }
  data.update_time = dayjs().toISOString()
  const { id, ...updated_data } = data
  const updated_ = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()

  const { id: _, update_time, ...updated_log } = updated_[0]
  await db.insert(submission_logs).values({
    ...updated_log,
    submiting_id: submission_id,
    submitter_name,
    create_time: update_time,
  })

  return updated_[0]
}

export async function update_batch(data_batch: UpdateSubmission[], submitter: User): Promise<Submission[]> {
  for (const data of data_batch) {
    const { success, error } = submission_zu.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const updates: Submission[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      const preserved_ = await tx.select().from(model).where(eq(model.id, data.id))
      if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })
      const preserved = preserved_[0]

      if (preserved.form_id !== data.form_id || preserved.point_id !== data.point_id)
        throw new Response("form_id, point_id not allowed change", { status: Status.BadRequest })

      const submitter_name = submitter.name_chinese || `user_${submitter.id}`
      const { id: submission_id, ...preserved_log } = preserved
      if (!(await getLogs(submission_id)).length) {
        await tx.insert(submission_logs).values({
          ...preserved_log,
          submiting_id: submission_id,
          submitter_name,
        })
      }

      data.update_time = dayjs().toISOString()
      const { id, ...updated_data } = data
      const updated_ = await tx.update(model).set(updated_data).where(eq(model.id, id)).returning()

      const { id: _, update_time, ...updated_log } = updated_[0]
      await tx.insert(submission_logs).values({
        ...updated_log,
        submiting_id: submission_id,
        submitter_name,
        create_time: update_time,
      })

      updates.push(updated_[0])
    }
  })

  return updates
}

export async function remove(id: Submission["id"]) {
  await db.delete(model).where(eq(model.id, id))
}

export async function remove_batch(ids: Submission["id"][]): Promise<void> {
  await db.transaction(async (tx) => {
    for (const id of ids) {
      await tx.delete(model).where(eq(model.id, id))
    }
  })
}

type dbSelect = PgSelectBuilder<undefined>

export async function restrict_submission_singleton(select: dbSelect, form_id: number, point_id: number) {
  const form = await select.from(forms).where(eq(forms.id, form_id))

  if (form.length && form[0].singleton_submiting) {
    const result = await select
      .from(model)
      .where(and(eq(model.form_id, form_id), eq(model.point_id, point_id)))

    if (result.length > 0) {
      throw new Response(`singleton_submission conflict form_id:${form_id} point_id:${point_id}`, {
        status: Status.Conflict,
      })
    }
  }
}
