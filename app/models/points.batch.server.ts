import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"
import dayjs from "dayjs"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as schema from "~/schema/points"
import { points, point_zi, type Point, type NewPoint, point_zu } from "~/schema/points"
import { NewSubmission, submission_zi, Submission } from "~/schema/submissions"
import { getLogs, restrict_submission_singleton } from "./submissions.server"
import { submissions } from "~/schema/submissions"
import { restrict_points_slug_unique } from "./points.server"
import { type User } from "~/schema/users"
import { point_logs } from "~/schema/point_logs"

const db = drizzle(process.env.DATABASE_URL!, { schema, logger: false })

export type NewPointForBatch = NewPoint & {
  parent_slug: string | undefined
}

export async function insert_batch(data_batch: NewPointForBatch[]): Promise<Point[]> {
  for (const data of data_batch) {
    const { success, error } = point_zi.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const inserts: Point[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      await restrict_points_slug_unique(data.project_id, data.slug)

      if (data.parent_slug) {
        const parents = await tx
          .select()
          .from(points)
          .where(and(eq(points.project_id, data.project_id), eq(points.slug, data.parent_slug)))
        if (parents.length === 0) {
          throw new Response(`parent not found ${data.slug}`, { status: Status.NotFound })
        }
        data.parent_id = parents[0].id
        delete data.parent_slug
      }

      const inserted_ = await tx.insert(points).values(data).returning()
      inserts.push(inserted_[0])
    }
  })
  return inserts
}

export async function update_batch(data_batch: Point[], submitter: User): Promise<Point[]> {
  for (const data of data_batch) {
    const { success, error } = point_zu.safeParse(data)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }
  }

  const updates: Point[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      const preserved_ = await tx.select().from(points).where(eq(points.id, data.id))
      if (preserved_.length === 0) throw new Response(`not found ${data.id}`, { status: Status.NotFound })
      const preserved = preserved_[0]

      await restrict_points_slug_unique(data.project_id, data.slug, preserved)

      const submitter_name = submitter.name_chinese || `user_${submitter.id}`
      const { id: point_id, ...preserved_log } = preserved
      if (!(await getLogs(point_id)).length) {
        await tx.insert(point_logs).values({
          ...preserved_log,
          point_id,
          submitter_name,
          create_time: preserved.create_time ?? dayjs().toISOString(),
        })
      }

      data.update_time = dayjs().toISOString()
      const { id, ...updated_data } = data
      const updated_ = await tx.update(points).set(updated_data).where(eq(points.id, id)).returning()

      const { id: _, update_time, ...updated_log } = updated_[0]
      await tx.insert(point_logs).values({
        ...updated_log,
        point_id,
        submitter_name,
        create_time: update_time ?? dayjs().toISOString(),
      })

      updates.push(updated_[0])
    }
  })
  return updates
}

export type NewPointSubmissionForBatch = NewPointForBatch & {
  parent_slug: string | undefined
  submiting: NewSubmission
}

export type PointSubmissionForBatch = Point & {
  submiting: Submission
}

export async function insert_batch_with_submission(
  data_batch: NewPointSubmissionForBatch[],
): Promise<PointSubmissionForBatch[]> {
  for (const data of data_batch) {
    const { submiting: submission0, ...point } = data

    const { success, error } = point_zi.safeParse(point)
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }

    const { success: success0, error: error0 } = submission_zi.safeParse(submission0)
    if (!success0) {
      throw new Response(`${error0}`, { status: Status.BadRequest })
    }
  }

  const batch_result: PointSubmissionForBatch[] = []
  await db.transaction(async (tx) => {
    for (const data of data_batch) {
      const { submiting: submission, ...point } = data

      await restrict_points_slug_unique(point.project_id, point.slug)

      if (point.parent_slug) {
        const parent_result = await tx
          .select()
          .from(points)
          .where(and(eq(points.project_id, point.project_id), eq(points.slug, point.parent_slug)))
        if (parent_result.length === 0) {
          throw new Response(`parent not found ${point.slug}`, { status: Status.NotFound })
        }
        point.parent_id = parent_result[0].id
        delete point.parent_slug
      }

      const inserted = await tx.insert(points).values(point).returning()

      submission.point_id = inserted[0].id
      await restrict_submission_singleton(tx.select(), submission.form_id, submission.point_id)

      const submission_inserted = await tx.insert(submissions).values(submission).returning()
      const result: PointSubmissionForBatch = {
        ...inserted[0],
        submiting: submission_inserted[0],
      }

      batch_result.push(result)
    }
  })

  return batch_result
}
