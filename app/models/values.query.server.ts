// import "dotenv/config"
// import { drizzle } from "drizzle-orm/node-postgres"
// import { eq, and, inArray, sql } from "drizzle-orm"

// import * as submiting_schema from "~/schema/submitings"
// import * as values_schema from "~/schema/values"
// import * as points_schema from "~/schema/points"
// import * as forms_schema from "~/schema/forms"
// import * as units_schema from "~/schema/units"

// import { submitings } from "~/schema/submitings"
// import { forms } from "~/schema/forms"
// import { type PointType } from "~/schema/point_types"
// import { FormForQuery, PointForQuery, SubmitingForQuery, ValueForQuery } from "~/schema/query"
// import { eq_integer_array, eq_optional_integer, eq_optional_point_types, eq_optional_text } from "~/utils/sql"
// import { OrderScending, sort_query_values } from "~/utils/sorts"

// export type QueryValues = SubmitingForQuery & {
//   values: ValueForQuery[]
//   form: FormForQuery
//   point: PointForQuery
// }

// const db = drizzle(process.env.DATABASE_URL!, {
//   schema: { ...submiting_schema, ...values_schema, ...points_schema, ...forms_schema, ...units_schema },
//   logger: true,
// })

// export async function queryValuesFool(
//   project_id: number,
//   point_type: PointType | undefined,
//   form_slug: string | undefined,
//   point_id: number | undefined,
//   offset: number | undefined,
//   limit: number | undefined,
//   orders_by: string[],
//   orders: OrderScending[],
// ): Promise<QueryValues[]> {
//   const forms_result = await db.query.forms.findMany({
//     columns: {
//       id: true,
//     },
//     where: and(
//       eq(forms.project_id, project_id),
//       point_type ? eq(forms.point_type, point_type) : undefined,
//       form_slug ? eq(forms.slug, form_slug) : undefined,
//     ),
//     with: {
//       units: {
//         columns: {
//           id: true,
//           label: true,
//           slug: true,
//         },
//       },
//     },
//   })
//   const form_ids = forms_result.map((f) => f.id)

//   const submiting_result: QueryValues[] = await db.query.submitings.findMany({
//     columns: {
//       id: true,
//       update_time: true,
//     },
//     where: and(
//       inArray(submitings.form_id, form_ids),
//       point_id ? eq(submitings.point_id, point_id) : undefined,
//     ),
//     with: {
//       values: {
//         columns: {
//           id: true,
//           unit_id: true,
//           value: true,
//         },
//       },
//       form: {
//         columns: {
//           id: true,
//           name: true,
//           slug: true,
//           point_type: true,
//         },
//       },
//       point: {
//         columns: {
//           id: true,
//           name: true,
//           slug: true,
//           type: true,
//           status: true,
//         },
//       },
//     },
//     offset,
//     limit,
//   })

//   for (const data of submiting_result) {
//     const form0 = forms_result.find((f) => f.id === data.form.id)!
//     const units_map = new Map(form0.units.map((u) => [u.id, u]))
//     for (const value of data.values) {
//       const unit = units_map.get(value.unit_id)
//       value.slug = unit?.slug ?? undefined
//       value.label = unit?.label ?? undefined
//     }
//   }

//   sort_query_values(submiting_result, orders_by, orders)
//   return submiting_result
// }

// const forms_prepare = db.query.forms
//   .findMany({
//     columns: {
//       id: true,
//     },
//     where: and(
//       eq(forms.project_id, sql.placeholder("project_id")),
//       eq_optional_point_types(forms.point_type, "point_type"),
//       eq_optional_text(forms.slug, "form_slug"),
//     ),
//     with: {
//       units: {
//         columns: {
//           id: true,
//           slug: true,
//           label: true,
//         },
//       },
//     },
//   })
//   .prepare("query_values_forms")

// const submiting_prepare = db.query.submitings
//   .findMany({
//     columns: {
//       id: true,
//       update_time: true,
//     },
//     where: and(
//       eq_integer_array(submitings.form_id, "form_ids"),
//       eq_optional_integer(submitings.point_id, "point_id"),
//     ),
//     with: {
//       values: {
//         columns: {
//           id: true,
//           unit_id: true,
//           value: true,
//         },
//       },
//       form: {
//         columns: {
//           id: true,
//           name: true,
//           slug: true,
//           point_type: true,
//         },
//       },
//       point: {
//         columns: {
//           id: true,
//           name: true,
//           slug: true,
//           type: true,
//           status: true,
//         },
//       },
//     },
//     offset: sql.placeholder("offset"),
//     limit: sql.placeholder("limit"),
//   })
//   .prepare("query_values_submiting")

// export async function queryValues(
//   project_id: number,
//   point_type: PointType | undefined,
//   form_slug: string | undefined,
//   point_id: number | undefined,
//   offset: number | undefined,
//   limit: number | undefined,
//   orders_by: string[],
//   orders: OrderScending[],
// ): Promise<QueryValues[]> {
//   const forms_result = await forms_prepare.execute({ project_id, point_type, form_slug })
//   const submiting_result: QueryValues[] = await submiting_prepare.execute({
//     form_ids: forms_result.map((r) => r.id).join(","),
//     point_id,
//     offset,
//     limit,
//   })

//   for (const data of submiting_result) {
//     const form = forms_result.find((f) => f.id === data.form.id)!
//     const units_map = new Map(form.units.map((u) => [u.id, u]))
//     for (const value of data.values) {
//       const unit = units_map.get(value.unit_id)
//       value.slug = unit?.slug ?? undefined
//       value.label = unit?.label ?? undefined
//     }
//   }

//   sort_query_values(submiting_result, orders_by, orders)
//   return submiting_result
// }

// export type QueryValuesFlat = SubmitingForQuery & {
//   [key: string]: number | string | string[]
// }

// export async function queryValuesFlat(
//   project_id: number,
//   point_type: PointType | undefined,
//   form_slug: string | undefined,
//   point_id: number | undefined,
//   offset: number | undefined,
//   limit: number | undefined,
// ): Promise<QueryValuesFlat[]> {
//   const forms_result = await forms_prepare.execute({ project_id, point_type, form_slug })
//   const submiting_result = await submiting_prepare.execute({
//     form_ids: forms_result.map((r) => r.id).join(","),
//     point_id,
//     offset,
//     limit,
//   })

//   const result: QueryValuesFlat[] = []
//   for (const data of submiting_result) {
//     const { id, update_time, values, form, point } = data

//     const form0 = forms_result.find((f) => f.id === form.id)!
//     const units_map = new Map(form0.units.map((u) => [u.id, u.slug]))

//     const item: QueryValuesFlat = { id, update_time }
//     for (const value of values) {
//       item[`${point.slug}_${form.slug}_${units_map.get(value.unit_id)}`] = value.value
//     }
//     result.push(item)
//   }

//   return result
// }
