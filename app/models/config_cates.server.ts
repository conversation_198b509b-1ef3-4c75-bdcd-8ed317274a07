import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as projects_schema from "~/schema/projects"
import * as config_cates_schema from "~/schema/config_cates"

import {
  config_cates as model,
  config_cate_zi,
  config_cate_zu,
  type ConfigCate,
  type NewConfigCate,
  type UpdateConfigCate,
} from "~/schema/config_cates"

const db = drizzle(process.env.DATABASE_URL!, { schema: { ...config_cates_schema, ...projects_schema } })

export const get = async (id: ConfigCate["id"]): Promise<ConfigCate | undefined> =>
  await db.query.config_cates.findFirst({
    where: eq(model.id, id),
  })

export async function find(project_id?: number, slug?: string, name?: string) {
  const result = await db.query.config_cates.findMany({
    where: and(
      project_id ? eq(model.project_id, project_id) : undefined,
      slug ? ilike(model.slug, slug) : undefined,
      name ? ilike(model.name, `%${name}%`) : undefined,
    ),
  })

  return result
}

export async function insert(data: NewConfigCate): Promise<ConfigCate> {
  const { success, error } = config_cate_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_cate_slug_unique(data.project_id, data.slug)

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateConfigCate): Promise<ConfigCate> {
  const { success, error } = config_cate_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  await restrict_cate_slug_unique(data.project_id, data.slug, preserved_[0])

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: ConfigCate["id"]) {
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_cate_slug_unique = async (
  project_id?: number | null,
  slug?: string,
  preserved?: ConfigCate,
) => {
  if (preserved) {
    if (!(project_id && project_id !== preserved.project_id) && !(slug && slug !== preserved.slug)) {
      return
    }

    project_id = project_id ?? preserved.project_id ?? undefined
    slug = slug ?? preserved.slug
  }

  if (!project_id) return

  const result = await db
    .select()
    .from(model)
    .where(and(eq(model.project_id, project_id), eq(model.slug, slug!)))

  if (result.length > 0) {
    throw new Response(`restrict_points_slug_unique ${slug}`, { status: Status.Conflict })
  }
}
