import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import bcrypt from "bcryptjs"
import { z } from "zod"
import { eq, getTableColumns, ilike } from "drizzle-orm"
import { AuthorizationError } from "remix-auth"
import { HttpStatusCode as Status } from "~/utils/http_code"

import {
  accounts as model,
  account_zi,
  account_zu,
  type Account,
  type NewAccount,
  type UpdateAccount,
  type NativeAccount,
} from "~/schema/accounts"
import * as accounts_schema from "~/schema/accounts"
import * as users_schema from "~/schema/users"
import { users, user_zi } from "~/schema/users"
import { sanitizeLike } from "~/utils/sanitize_input"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...accounts_schema, ...users_schema },
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const { password, ...rest } = getTableColumns(model)

export const get = async (id: Account["id"]): Promise<Account | undefined> =>
  await db.query.accounts
    .findMany({
      columns: {
        password: false,
      },
      where: eq(model.id, id),
      with: {
        users: true,
      },
    })
    .then((rows) => rows.at(0))

// const result = await db
//   .select({ ...rest })
//   .from(model)
//   .where(eq(model.id, id))

export const find = async (name?: string, offset = 0, limit = 20): Promise<Account[]> => {
  return await db.query.accounts.findMany({
    columns: {
      password: false,
    },
    where: name ? ilike(model.name, `%${sanitizeLike(name)}%`) : undefined,
    with: {
      users: true,
    },
    offset,
    limit,
  })
}

const signup_z = account_zi.extend({
  users: z.array(user_zi).optional(),
})

export async function signUp(data: NewAccount): Promise<Account> {
  const { success, error } = signup_z.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.name, data.name))
  if (preserved_.length > 0) {
    throw new Response("name exist", { status: Status.Conflict })
  }

  return await db.transaction(async (tx) => {
    data.password = await bcrypt.hash(data.password, 10)
    const inserted_ = await tx.insert(model).values(data).returning()
    const { password: _password, ...account } = inserted_[0] as NativeAccount & Account

    if (data.users) {
      account.users = []
      for (const user of data.users) {
        user.account_id = account.id
        const inserted_ = await tx.insert(users).values(user).returning()
        account.users.push(inserted_[0])
      }
    }

    return account
  })
}

export async function signIn(name: Account["name"], password: string): Promise<Account> {
  const result = await db.select().from(model).where(eq(model.name, name))
  if (result.length === 0) throw new AuthorizationError("invalid credentials")
  const data = result[0]

  const isValid = await bcrypt.compare(password, data.password)
  if (!isValid) throw new AuthorizationError("invalid credentials")

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password: _password, ...account } = data
  return account
}

export async function update(data: UpdateAccount) {
  const { success, error } = account_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })
  const account = preserved_[0]

  if (data.name && account.name !== data.name) {
    const result = await db.select().from(model).where(eq(model.name, data.name))
    if (result.length > 0) {
      throw new Response("name exist", { status: Status.Conflict })
    }
  }

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function updatePassword(id: Account["id"], password: string) {
  const preserved_ = await db.select().from(model).where(eq(model.id, id))
  if (preserved_.length === 0) throw new Response("account not found", { status: Status.NotFound })

  const hashedPassword = await bcrypt.hash(password, 10)
  const result = await db.update(model).set({ password: hashedPassword }).where(eq(model.id, id)).returning()
  return result[0]
}
