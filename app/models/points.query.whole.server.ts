import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, inArray, sql } from "drizzle-orm"

import * as submission_schema from "~/schema/submissions"
import * as points_schema from "~/schema/points"
import * as forms_schema from "~/schema/forms"
import * as units_schema from "~/schema/units"

import { points as model } from "~/schema/points"
import { forms } from "~/schema/forms"
import { submissions } from "~/schema/submissions"
import { in_integer_array } from "~/utils/sql"
import type { PointForQueryWithValues } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...submission_schema, ...points_schema, ...forms_schema, ...units_schema },
  // logger: true,
})

export const find = async (project_id: number): Promise<PointForQueryWithValues[]> => {
  const points_result = await db.query.points.findMany({
    columns: {
      id: true,
      // name: true,
      slug: true,
      type: true,
      status: true,
    },
    where: and(eq(model.project_id, project_id)),
    with: {
      parent: {
        columns: {
          // name: true,
          slug: true,
        },
      },
    },
  })

  const forms_result = await db.query.forms.findMany({
    columns: {
      id: true,
    },
    where: and(eq(forms.project_id, project_id), eq(forms.slug, "baseForm")),
  })
  const form_ids = forms_result.map((f) => f.id)

  const submission_result = await db.query.submissions.findMany({
    columns: {
      values: true,
      point_id: true,
    },
    where: inArray(submissions.form_id, form_ids),
  })

  return points_result.map((point) => ({
    ...point,
    parent: point.parent ?? undefined,
    values: submission_result.find((s) => s.point_id === point.id)?.values ?? undefined,
  }))
}

const points_prepare = db.query.points
  .findMany({
    columns: {
      id: true,
      // name: true,
      slug: true,
      type: true,
      status: true,
      parent_id: true,
    },
    where: and(eq(model.project_id, sql.placeholder("project_id"))),
    // with: {
    //   parent: {
    //     columns: {
    //       // name: true,
    //       slug: true,
    //     },
    //   },
    // },
  })
  .prepare("points_query_points")

const forms_prepare = db.query.forms
  .findMany({
    columns: {
      id: true,
    },
    where: and(eq(forms.project_id, sql.placeholder("project_id")), eq(forms.slug, "baseForm")),
  })
  .prepare("points_query_forms")

const submission_prepare = db.query.submissions
  .findMany({
    columns: {
      values: true,
      point_id: true,
    },
    where: in_integer_array(submissions.form_id, "form_ids"),
  })
  .prepare("points_query_submission")

export async function find_prepare(project_id: number): Promise<PointForQueryWithValues[]> {
  const points_result = await points_prepare.execute({ project_id })
  const forms_result = await forms_prepare.execute({ project_id })
  const submission_result = await submission_prepare.execute({
    form_ids: forms_result.map((r) => r.id).join(","),
  })

  return points_result.map((point) => ({
    ...point,
    // parent: point.parent ?? undefined,
    values: submission_result.find((s) => s.point_id === point.id)?.values ?? undefined,
  }))
}
