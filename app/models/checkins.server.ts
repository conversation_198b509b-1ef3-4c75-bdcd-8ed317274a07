import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, gte, lte } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  checkins as model,
  checkin_zi,
  checkin_zu,
  type CheckIn,
  type NewCheckIn,
  type UpdateCheckIn,
} from "~/schema/checkins"
import { users } from "~/schema/users"
import { checkin_devices } from "~/schema/checkin_devices"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: number): Promise<CheckIn | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (
  project_id?: number,
  user_id?: number,
  device?: string,
  time_start?: string,
  time_end?: string,
) =>
  await db
    .select()
    .from(model)
    .where(
      and(
        project_id ? eq(model.project_id, project_id) : undefined,
        user_id ? eq(model.user_id, user_id) : undefined,
        device ? eq(model.device_id, device) : undefined,
        time_start ? gte(model.create_time, time_start) : undefined,
        time_end ? lte(model.create_time, time_end) : undefined,
      ),
    )

export async function insert(data: NewCheckIn, identity: string): Promise<CheckIn> {
  const { success, error } = checkin_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const users_ = await db.select().from(users).where(eq(users.identity, identity))
  if (users_.length == 0) {
    throw new Response("identity invalid", { status: Status.Conflict })
  }

  const devices_ = await db.select().from(checkin_devices).where(eq(checkin_devices.id, data.device_id))
  if (devices_.length == 0) {
    throw new Response("device_id invalid", { status: Status.Conflict })
  }

  data.user_id = users_[0].id
  data.project_id = devices_[0].project_id

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateCheckIn) {
  const { success, error } = checkin_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: number) {
  await db.delete(model).where(eq(model.id, id))
}
