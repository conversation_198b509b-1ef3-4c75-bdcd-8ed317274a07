import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and, desc } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as order_replies_schema from "~/schema/order_replies"
import * as orders_schema from "~/schema/orders"
import * as users_schema from "~/schema/users"

import {
  order_replies as model,
  order_reply_zi,
  order_reply_zu,
  type OrderReply,
  type NewOrderReply,
  type UpdateOrderReply,
} from "~/schema/order_replies"
import type { OrderReplyForQuery, UserForQuery } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...order_replies_schema, ...orders_schema, ...users_schema },
})

export type OrderReplyWith = OrderReplyForQuery & {
  creator?: UserForQuery | null
}

export const get = async (id: OrderReply["id"]): Promise<OrderReplyWith | undefined> =>
  await db.query.order_replies.findFirst({
    where: eq(model.id, id),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
          disable: true,
        },
      },
    },
  })

export async function find(
  order_id: number | undefined,
  content: string | undefined,
  creator_id: number | undefined,
  offset: number | undefined,
  limit: number | undefined,
): Promise<OrderReplyWith[]> {
  const result = await db.query.order_replies.findMany({
    where: and(
      order_id ? eq(model.order_id, order_id) : undefined,
      creator_id ? eq(model.creator_id, creator_id) : undefined,
      content ? ilike(model.content, `%${content}%`) : undefined,
    ),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
          disable: true,
        },
      },
    },
    orderBy: [desc(model.create_time)],
    limit,
    offset,
  })

  return result
}

export async function insert(data: NewOrderReply): Promise<OrderReply> {
  const { success, error } = order_reply_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateOrderReply): Promise<OrderReply> {
  const { success, error } = order_reply_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: OrderReply["id"]) {
  await db.delete(model).where(eq(model.id, id))
}
