// import "dotenv/config"
// import { drizzle } from "drizzle-orm/node-postgres"
// import { eq, and } from "drizzle-orm"

// import { values as model, value_zi, value_zu, type Value, type NewValue, UpdateValue } from "~/schema/values"
// import { HttpStatusCode as Status } from "~/utils/http_code"

// const db = drizzle(process.env.DATABASE_URL!)

// export async function get(id: Value["id"]): Promise<Value | null> {
//   const result = await db.select().from(model).where(eq(model.id, id))
//   if (result.length === 0) return null
//   return result[0]
// }

// export async function find(submiting_id: number, unit_id: number): Promise<Value[]> {
//   const result = await db
//     .select()
//     .from(model)
//     .where(and(eq(model.submiting_id, submiting_id), eq(model.unit_id, unit_id)))

//   return result
// }

// export async function insert(data: NewValue): Promise<Value> {
//   const { success, error } = value_zi.safeParse(data)
//   if (!success) {
//     throw new Response(`${error}`, { status: Status.BadRequest })
//   }

//   await restrict_values_unique(data.unit_id, data.submiting_id)

//   const result = await db.insert(model).values(data).returning()
//   return result[0]
// }

// export async function update(data: UpdateValue): Promise<Value> {
//   const { success, error } = value_zu.safeParse(data)
//   if (!success) {
//     throw new Response(`${error}`, { status: Status.BadRequest })
//   }

//   const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
//   if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

//   await restrict_values_unique(data.unit_id, data.submiting_id, preserved_[0])

//   const { id, ...updated_data } = data
//   const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
//   return result[0]
// }

// export async function remove(id: Value["id"]) {
//   await db.delete(model).where(eq(model.id, id))
// }

// export const restrict_values_unique = async (unit_id?: number, submiting_id?: number, preserved?: Value) => {
//   if (preserved) {
//     if (
//       !(unit_id && unit_id !== preserved.unit_id) &&
//       !(submiting_id && submiting_id !== preserved.submiting_id)
//     ) {
//       return
//     }

//     unit_id = unit_id ?? preserved.unit_id
//     submiting_id = submiting_id ?? preserved.submiting_id
//   }

//   const result = await db
//     .select()
//     .from(model)
//     .where(and(eq(model.unit_id, unit_id!), eq(model.submiting_id, submiting_id!)))

//   if (result.length > 0) {
//     throw new Response(`restrict_values_unique ${unit_id} ${submiting_id}`, { status: Status.Conflict })
//   }
// }
