import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and, count } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  cabinets as model,
  cabinet_zi,
  cabinet_zu,
  type Cabinet,
  type NewCabinet,
  type UpdateCabinet,
} from "~/schema/cabinets"
import { cabinet_materials } from "~/schema/cabinet_materials"

import * as cabinets_schema from "~/schema/cabinets"
import * as cabinet_materials_schema from "~/schema/cabinet_materials"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...cabinets_schema, ...cabinet_materials_schema },
  // logger: true,
})

export const get = async (id: Cabinet["id"]): Promise<Cabinet | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const get_count = async (
  project_id: number,
  point_id?: number,
  material_id?: number,
  room_code_group?: boolean,
) => {
  let query = db
    .select({ count: count() })
    .from(model)
    .where(
      and(
        eq(model.project_id, project_id),
        point_id ? eq(model.point_id, point_id) : undefined,
        material_id ? eq(model.material_id, material_id) : undefined,
      ),
    )
    .$dynamic()

  if (room_code_group) {
    query = query.groupBy(model.room_code)
  }

  const [result] = await query
  return result
}

export async function find(
  project_id: number,
  point_id?: number,
  material_id?: number,
  code?: string,
  room_code?: string,
  type?: string,
  offset = 0,
  limit = 20,
) {
  const result = await db.query.cabinets.findMany({
    where: and(
      eq(model.project_id, project_id),
      point_id ? eq(model.point_id, point_id) : undefined,
      material_id ? eq(model.material_id, material_id) : undefined,
      code ? ilike(model.code, code) : undefined,
      room_code ? ilike(model.room_code, room_code) : undefined,
      type ? ilike(model.type, type) : undefined,
    ),
    with: {
      materials: true,
    },
    offset,
    limit,
  })

  return result
}

export async function insert(data: NewCabinet): Promise<Cabinet> {
  const { success, error } = cabinet_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateCabinet) {
  const { success, error } = cabinet_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: Cabinet["id"]) {
  const result = await db.select().from(cabinet_materials).where(eq(cabinet_materials.cabinet_id, id))
  if (result.length > 0) {
    throw new Response("has cabinet_materials", { status: Status.Conflict })
  }

  await db.delete(model).where(eq(model.id, id))
}
