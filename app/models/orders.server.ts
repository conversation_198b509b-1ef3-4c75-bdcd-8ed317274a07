import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and, arrayOverlaps, or, inArray, count } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as orders_schema from "~/schema/orders"
import * as projects_schema from "~/schema/projects"
import * as points_schema from "~/schema/points"
import * as users_schema from "~/schema/users"

import {
  orders as model,
  order_zi,
  order_zu,
  type Order,
  type NewOrder,
  type UpdateOrder,
  type OrderStatus,
} from "~/schema/orders"
import { projects } from "~/schema/projects"
import { points } from "~/schema/points"
import { type User, users } from "~/schema/users"
import smsSend from "~/utils/sms"
import { sanitizeLike } from "~/utils/sanitize_input"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...orders_schema, ...points_schema, ...users_schema, ...projects_schema },
})

export const get = async (id: Order["id"]): Promise<Order | undefined> =>
  await db.query.orders
    .findMany({
      where: eq(model.id, id),
    })
    .then((rows) => rows.at(0))

export const get_count = async (
  project_id?: number,
  point_id?: number,
  creator_id?: number,
  handler_id?: number,
  order_status?: OrderStatus,
) =>
  await db
    .select({ count: count() })
    .from(model)
    .where(
      and(
        project_id ? eq(model.project_id, project_id) : undefined,
        point_id ? eq(model.point_id, point_id) : undefined,
        creator_id ? eq(model.creator_id, creator_id) : undefined,
        handler_id ? eq(model.handler_id, handler_id) : undefined,
        order_status ? eq(model.status, order_status) : undefined,
      ),
    )
    .then((rows) => rows.at(0))

export async function find(
  self_id?: number,
  project_id?: number,
  point_id?: number,
  creator_id?: number,
  handler_id?: number,
  order_status?: OrderStatus | undefined,
  title?: string,
  offset?: number,
  limit?: number,
) {
  if (self_id) {
    const projects_ = await db.query.projects.findMany({
      where: or(eq(projects.manager_id, self_id), arrayOverlaps(projects.users, [self_id])),
    })

    return await db.query.orders.findMany({
      where: and(
        inArray(
          model.project_id,
          projects_.map((p) => p.id),
        ),
        creator_id ? eq(model.creator_id, creator_id) : undefined,
        handler_id ? eq(model.handler_id, handler_id) : undefined,
        order_status ? eq(model.status, order_status) : undefined,
        title ? ilike(model.title, `%${sanitizeLike(title)}%`) : undefined,
      ),
      limit,
      offset,
    })
  }

  return await db.query.orders.findMany({
    where: and(
      project_id ? eq(model.project_id, project_id) : undefined,
      point_id ? eq(model.point_id, point_id) : undefined,
      creator_id ? eq(model.creator_id, creator_id) : undefined,
      handler_id ? eq(model.handler_id, handler_id) : undefined,
      order_status ? eq(model.status, order_status) : undefined,
      title ? ilike(model.title, `%${sanitizeLike(title)}%`) : undefined,
    ),
    limit,
    offset,
  })
}

export async function insert(data: NewOrder): Promise<Order> {
  const { success, error } = order_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const projects_ = await db.query.projects.findMany({
    where: eq(projects.id, data.project_id),
  })
  if (projects_.length == 0) throw new Response("project not found", { status: Status.NotFound })
  const manager_id = projects_[0].order_manager_id

  if (!data.handler_id) {
    data.handler_id = projects_[0].order_handler_id
  }
  const result = await db.insert(model).values(data).returning()

  const handlers = await db.query.users.findMany({
    where: or(
      data.handler_id ? eq(users.id, data.handler_id) : undefined,
      manager_id ? eq(users.id, manager_id) : undefined,
    ),
  })
  if (!handlers.length) throw new Response("handler not found", { status: Status.NotFound })

  for (const handler of handlers) {
    if (handler.phone && !isNaN(parseInt(handler.phone))) {
      await smsSend("2061012309619", smsText(data.title), handler.phone)
    }
  }

  return result[0]
}

export async function update(data: UpdateOrder): Promise<Order> {
  const { success, error } = order_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { id, ...updated_data } = data
  const updated_ = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  const updated = updated_[0]

  if (updated.handler_id && preserved_[0].handler_id !== updated.handler_id) {
    const handler = await db.query.users.findFirst({
      where: eq(users.id, updated.handler_id),
    })
    if (!handler) throw new Response("handler not found", { status: Status.NotFound })
    if (handler.phone && !isNaN(parseInt(handler.phone))) {
      await smsSend("2061012309619", smsText(updated.title), handler.phone)
    }
  }

  return updated
}

export async function remove(id: Order["id"], user: User) {
  if (user.role !== "admin") {
    const order_ = await db.query.orders.findMany({
      where: eq(model.id, id),
      with: {
        project: true,
      },
    })
    if (!order_.length) throw new Response("not found", { status: Status.NotFound })
    const order = order_[0]

    if (order.creator_id !== user.id && order.project.manager_id !== user.id) {
      throw new Response("not allowed", { status: Status.Forbidden })
    }
  }

  await db.delete(model).where(eq(model.id, id))
}

export type GrafanaAlert = {
  labels: {
    alertname: string
    point: string
    projectId: string
  }
  startsAt: string
  status: string
}

export async function insertFromGrafana(alerts: GrafanaAlert[]): Promise<Order[]> {
  const results: Order[] = []
  for (const alert of alerts) {
    const { labels, startsAt, status } = alert
    if (status !== "firing") continue

    // const preserved_ = await db
    //   .select()
    //   .from(model)
    //   .where(and(eq(model.title, labels.alertname), eq(model.create_time, startsAt)))
    // if (preserved_.length > 0) throw new Response("exist", { status: Status.Conflict })

    const points_ = await db.query.points.findMany({
      where: eq(points.slug, labels.point),
    })
    if (points_.length == 0) {
      console.warn("point not found for alert", labels.point)
      continue
    }

    const project_id = parseInt(labels.projectId)
    const project_ = await db.query.projects.findMany({
      where: eq(projects.id, project_id),
    })
    if (project_.length == 0) {
      console.warn("project not found for alert", project_id)
      continue
    }
    const handler_id = project_[0].order_handler_id

    const result = await insert({
      project_id,
      handler_id,
      point_id: points_[0].id,
      title: labels.alertname,
      create_time: startsAt,
      creator_id: 1,
    })
    results.push(result)

    if (!handler_id) {
      console.warn("no manager_id for project", project_id, "alert")
      continue
    }

    const handler_ = await db.query.users.findMany({
      where: eq(users.id, handler_id),
    })
    if (!handler_.length) {
      console.warn("handler not found for alert", handler_id)
      continue
    }
    const handler = handler_[0]

    if (handler.phone && !isNaN(parseInt(handler.phone))) {
      await smsSend("2061012309619", smsText(labels.alertname), handler.phone)
    }
  }
  return results
}

const smsText = (title: string) =>
  `您有新的工单${title}，请及时处理。http://218.93.190.116:3000/#/mobile/order`
