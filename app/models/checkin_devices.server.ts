import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  checkin_devices as model,
  checkin_device_zi,
  checkin_device_zu,
  type CheckInDevice,
  type NewCheckInDevice,
  type UpdateCheckInDevice,
} from "~/schema/checkin_devices"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (serial_number: string): Promise<CheckInDevice | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, serial_number))
    .then((rows) => rows.at(0))

export const find = async (project_id?: number) =>
  await db
    .select()
    .from(model)
    .where(project_id ? eq(model.project_id, project_id) : undefined)

export async function insert(data: NewCheckInDevice): Promise<CheckInDevice> {
  const { success, error } = checkin_device_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateCheckInDevice) {
  const { success, error } = checkin_device_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.serial_number))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { serial_number, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, serial_number)).returning()
  return result[0]
}

export async function remove(serial_number: string) {
  await db.delete(model).where(eq(model.id, serial_number))
}
