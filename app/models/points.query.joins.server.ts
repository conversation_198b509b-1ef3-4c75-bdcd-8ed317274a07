import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and, aliasedTable, inArray, getTableColumns } from "drizzle-orm"

import { submissions } from "~/schema/submissions"
import { forms } from "~/schema/forms"
import { points } from "~/schema/points"
import { type PointType } from "~/schema/point_types"
import { alias } from "drizzle-orm/pg-core"
import { Order } from "~/utils/params_parse"
import { PointForQueryWithValues } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  // logger: true,
})

// https://orm.drizzle.team/docs/data-querying#separate-subqueries-into-different-variables-and-then-use-them-in-the-main-query
export async function find(
  project_id: number,
  point_type: PointType | undefined,
  point_id: number | undefined,
  offset: number | undefined,
  limit: number | undefined,
  // orders: Order[],
): Promise<PointForQueryWithValues[]> {
  const forms_query = db
    .select({
      form_id: forms.id,
    })
    .from(forms)
    .where(and(eq(forms.project_id, project_id), eq(forms.slug, "baseForm")))
    .as("forms_units")

  const parent = alias(points, "parent")
  const points_query = db
    .select({
      id: points.id,
      name: points.name,
      slug: points.slug,
      type: points.type,
      status: points.status,
      parent: {
        id: parent.id,
        name: parent.name,
        slug: parent.slug,
        type: parent.type,
        status: points.status,
      },
    })
    .from(points)
    .where(
      and(
        eq(points.project_id, project_id),
        point_id ? eq(points.id, point_id) : undefined,
        point_type ? eq(points.type, point_type) : undefined,
      ),
    )
    .leftJoin(parent, eq(parent.id, points.parent_id))
    .as("points_with_parent")

  const result = await db
    .select({
      ...getTableColumns(submissions),
      point: {
        id: points_query.id,
        name: points_query.name,
        slug: points_query.slug,
        type: points_query.type,
        status: points_query.status,
      },
      parent: {
        id: points_query.parent.id,
        name: points_query.parent.name,
        slug: points_query.parent.slug,
        type: points_query.parent.type,
        status: points_query.parent.status,
      },
      form: {
        id: forms_query.form_id,
      },
    })
    .from(submissions)
    .offset(offset ?? 0)
    .limit(limit ?? 100)
    .leftJoin(forms_query, eq(forms_query.form_id, submissions.form_id))
    .innerJoin(points_query, eq(points_query.id, submissions.point_id))

  return result
}

// .leftJoinLateral(values, eq(values.submission_id, submission.id))
// wait: https://github.com/drizzle-team/drizzle-orm/pull/1079
