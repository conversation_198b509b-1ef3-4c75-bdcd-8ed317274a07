import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { and, eq } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  cabinet_materials as model,
  cabinet_material_zi,
  cabinet_material_zu,
  type CabinetMaterial,
  type NewCabinetMaterial,
  type UpdateCabinetMaterial,
} from "~/schema/cabinet_materials"
import * as schema from "~/schema/cabinet_materials"

const db = drizzle(process.env.DATABASE_URL!, { schema })

export const get = async (id: CabinetMaterial["id"]): Promise<CabinetMaterial | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (
  material_id?: number,
  cabinet_id?: number,
  type?: string,
  offset?: number,
  limit?: number,
) => {
  return await db.query.cabinet_materials.findMany({
    where: and(
      material_id ? eq(model.material_id, material_id) : undefined,
      cabinet_id ? eq(model.cabinet_id, cabinet_id) : undefined,
      type ? eq(model.type, type) : undefined,
    ),
    limit: limit ?? 100,
    offset,
  })
}

export async function insert(data: NewCabinetMaterial): Promise<CabinetMaterial> {
  const { success, error } = cabinet_material_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateCabinetMaterial): Promise<CabinetMaterial> {
  const { success, error } = cabinet_material_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: CabinetMaterial["id"]) {
  await db.delete(model).where(eq(model.id, id))
}
