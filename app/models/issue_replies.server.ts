import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and, desc } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as issue_replies_schema from "~/schema/issue_replies"
import * as issues_schema from "~/schema/issues"
import * as users_schema from "~/schema/users"

import {
  issue_replies as model,
  issue_reply_zi,
  issue_reply_zu,
  type IssueReply,
  type NewIssueReply,
  type UpdateIssueReply,
} from "~/schema/issue_replies"
import type { IssueReplyForQuery, UserForQuery } from "~/schema/query"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...issue_replies_schema, ...issues_schema, ...users_schema },
})

export type IssueReplyWith = IssueReplyForQuery & {
  creator?: UserForQuery | null
}

export const get = async (id: IssueReply["id"]): Promise<IssueReplyWith | undefined> =>
  await db.query.issue_replies.findFirst({
    where: eq(model.id, id),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
          disable: true,
        },
      },
    },
  })

export const find = async (
  issues_id?: number,
  content?: string,
  creator_id?: number,
  offset?: number,
  limit?: number,
): Promise<IssueReplyWith[]> => {
  const result = await db.query.issue_replies.findMany({
    where: and(
      issues_id ? eq(model.issue_id, issues_id) : undefined,
      creator_id ? eq(model.creator_id, creator_id) : undefined,
      content ? ilike(model.content, `%${content}%`) : undefined,
    ),
    with: {
      creator: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
          disable: true,
        },
      },
    },
    orderBy: [desc(model.create_time)],
    limit,
    offset,
  })

  return result
}

export async function insert(data: NewIssueReply): Promise<IssueReply> {
  const { success, error } = issue_reply_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateIssueReply): Promise<IssueReply> {
  const { success, error } = issue_reply_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: IssueReply["id"]) {
  await db.delete(model).where(eq(model.id, id))
}
