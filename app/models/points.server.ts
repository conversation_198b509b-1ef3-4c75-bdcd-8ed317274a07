import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, count, ilike, and, arrayContains } from "drizzle-orm"
import dayjs from "dayjs"

import { HttpStatusCode as Status } from "~/utils/http_code"
import * as points_schema from "~/schema/points"
import * as point_logs_schema from "~/schema/point_logs"
import * as issues_schema from "~/schema/issues"
import * as material_allocats_schema from "~/schema/material_allocats"

import {
  points as model,
  point_zi,
  point_zu,
  type Point,
  type NewPoint,
  type UpdatePoint,
  type PointStatus,
} from "~/schema/points"
import { issues } from "~/schema/issues"
import { material_allocats } from "~/schema/material_allocats"
import { point_logs } from "~/schema/point_logs"
import { type PointType } from "~/schema/point_types"
import { type User } from "~/schema/users"
import { sanitizeLike } from "~/utils/sanitize_input"

const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...points_schema, ...point_logs_schema, ...issues_schema, ...material_allocats_schema },
  // logger: true,
})

export const get = async (id: Point["id"]): Promise<Point | null> => {
  const result = await db.query.points.findMany({
    where: eq(model.id, id),
    with: {
      parent: true,
    },
  })

  return result[0] ?? null
}

export const get_count = async (
  project_id: number,
  point_type?: PointType,
  status?: PointStatus,
): Promise<{
  count: number
}> => {
  const [result] = await db
    .select({ count: count() })
    .from(model)
    .where(
      and(
        eq(model.project_id, project_id),
        point_type ? eq(model.type, point_type) : undefined,
        status ? eq(model.status, status) : undefined,
      ),
    )

  return result
}

export async function find(
  project_id: number,
  point_type?: PointType,
  status?: PointStatus,
  name?: string,
  offset?: number,
  limit?: number,
) {
  return await db.query.points.findMany({
    columns: {
      id: true,
      name: true,
      slug: true,
      type: true,
      status: true,
      parent_id: true,
    },
    where: and(
      eq(model.project_id, project_id),
      point_type ? eq(model.type, point_type) : undefined,
      name ? ilike(model.name, `%${sanitizeLike(name)}%`) : undefined,
      status ? eq(model.status, status) : undefined,
    ),
    // with: {
    //   parent: {
    //     columns: {
    //       id: true,
    //       // name: true,
    //       slug: true,
    //       type: true,
    //       status: true,
    //     },
    //   },
    // },
    limit,
    offset,
  })
}

export const getLogs = async (point_id: number) => {
  return await db.select().from(point_logs).where(eq(point_logs.point_id, point_id))
}

export const getNames = async (project_id: number): Promise<Record<number, string>> => {
  const result = await db.query.points.findMany({
    columns: {
      id: true,
      name: true,
    },
    where: eq(model.project_id, project_id),
  })

  //   return result.reduce(
  //     (acc, item) => {
  //       acc[item.id] = item.name
  //       return acc
  //     },
  //     {} as Record<number, string>,
  //   )
  return Object.fromEntries(result.map(({ id, name }) => [id, name]))
}

export const insert = async (data: NewPoint): Promise<Point> => {
  const { success, error } = point_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_points_slug_unique(data.project_id, data.slug)

  const [insert] = await db.insert(model).values(data).returning()
  return insert
}

export const update = async (data: UpdatePoint, submitter: User): Promise<Point> => {
  const { success, error } = point_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [preserved] = await db.select().from(model).where(eq(model.id, data.id))
  if (!preserved) throw new Response("not found", { status: Status.NotFound })

  await restrict_points_slug_unique(data.project_id, data.slug, preserved)

  const submitter_name = submitter.name_chinese || ""
  const { id: point_id, ...preserved_log } = preserved

  const logs = await getLogs(point_id)
  if (!logs.length) {
    await db.insert(point_logs).values({
      ...preserved_log,
      point_id,
      submitter_name,
      create_time: preserved.create_time ?? dayjs().toISOString(),
    })
  }

  data.update_time = dayjs().toISOString()
  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()

  const { id: _, update_time, ...updated_log } = updated
  await db.insert(point_logs).values({
    ...updated_log,
    point_id,
    submitter_name,
    create_time: update_time ?? dayjs().toISOString(),
  })

  return updated
}

export const remove = async (id: Point["id"]): Promise<void> => {
  const point = await get(id)
  if (!point) throw new Response("not found", { status: Status.NotFound })

  const [children] = await db.select().from(model).where(eq(model.parent_id, id))
  if (children) throw new Response("point has children", { status: Status.Conflict })

  const [allocats] = await db.select().from(material_allocats).where(eq(material_allocats.point_id, id))
  if (allocats) throw new Response("point has material_allocats", { status: Status.Conflict })

  const related_issues = await db.query.issues.findMany({
    where: arrayContains(issues.point_ids, [id]),
  })

  await Promise.all(
    related_issues.map(async (issue) => {
      const point_ids = issue.point_ids.filter((pid) => pid !== id)
      return db.update(issues).set({ point_ids }).where(eq(issues.id, issue.id))
    }),
  )

  await db.delete(model).where(eq(model.id, id))
}

export const restrict_points_slug_unique = async (
  project_id?: number,
  slug?: string,
  preserved?: Point,
): Promise<void> => {
  if (preserved) {
    const project_changed = project_id && project_id !== preserved.project_id
    const slug_changed = slug && slug !== preserved.slug

    if (!project_changed && !slug_changed) {
      return
    }

    project_id = project_id ?? preserved.project_id
    slug = slug ?? preserved.slug
  }

  if (!project_id || !slug) return

  const result = await db
    .select()
    .from(model)
    .where(and(eq(model.project_id, project_id), eq(model.slug, slug)))

  if (result.length > 0) {
    throw new Response(`restrict_points_slug_unique ${slug}`, { status: Status.Conflict })
  }
}
