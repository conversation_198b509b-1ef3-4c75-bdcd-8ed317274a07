import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import slug from "slug"
import { and, eq, ilike, arrayContains } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  projects as model,
  project_zi,
  project_zu,
  type Project,
  type NewProject,
  UpdateProject,
} from "~/schema/projects"
import { sanitizeLike } from "~/utils/sanitize_input"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Project["id"]): Promise<Project | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (user_id: number, name?: string) =>
  await db
    .select()
    .from(model)
    .where(
      and(
        arrayContains(model.users, [user_id]),
        name ? ilike(model.name, `%${sanitizeLike(name)}%`) : undefined,
      ),
    )

export const insert = async (data: NewProject): Promise<Project> => {
  if (!data.slug) {
    data.slug = slug(data.name)
  }

  const { success, error } = project_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [inserted] = await db.insert(model).values(data).returning()
  return inserted
}

export const update = async (data: UpdateProject): Promise<Project> => {
  const { success, error } = project_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return updated
}

export const remove = async (id: Project["id"]): Promise<void> => {
  await db.delete(model).where(eq(model.id, id))
}
