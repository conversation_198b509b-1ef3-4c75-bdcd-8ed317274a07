import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { ports as model, ports_zi, ports_zu, type Port, type NewPort, type UpdatePort } from "~/schema/ports"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Port["id"]): Promise<Port | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (project_id: number, material_id?: number, offset?: number, limit?: number) =>
  await db
    .select()
    .from(model)
    .where(
      and(eq(model.project_id, project_id), material_id ? eq(model.material_id, material_id) : undefined),
    )
    .offset(offset ?? 0)
    .limit(limit ?? 20)

export async function insert(data: NewPort): Promise<Port> {
  const { success, error } = ports_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_ports_no_unique(data.material_id, data.slot_no, data.port_no)

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdatePort): Promise<Port> {
  const { success, error } = ports_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  await restrict_ports_no_unique(data.material_id, data.slot_no, data.port_no, preserved_[0])

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: Port["id"]) {
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_ports_no_unique = async (
  material_id?: number,
  slot_no?: string,
  port_no?: string,
  preserved?: Port,
) => {
  if (preserved) {
    if (
      !(material_id && material_id !== preserved.material_id) &&
      !(slot_no && slot_no !== preserved.slot_no) &&
      !(port_no && port_no !== preserved.port_no)
    ) {
      return
    }

    material_id = material_id ?? preserved.material_id
    slot_no = slot_no ?? preserved.slot_no
    port_no = port_no ?? preserved.port_no
  }

  const result = await db
    .select()
    .from(model)
    .where(and(eq(model.material_id, material_id!), eq(model.slot_no, slot_no!), eq(model.port_no, port_no!)))

  if (result.length > 0) {
    throw new Response(`restrict_ports_no_unique ${port_no}`, { status: Status.Conflict })
  }
}
