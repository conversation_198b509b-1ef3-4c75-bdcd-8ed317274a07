import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, ilike, and, inArray, arrayContains, count } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import {
  issues as model,
  issus_zi,
  issus_zu,
  type Issue,
  type NewIssue,
  type UpdateIssue,
} from "~/schema/issues"
import * as projects_schema from "~/schema/projects"
import * as users_schema from "~/schema/users"
import * as points_schema from "~/schema/points"
import * as issues_schema from "~/schema/issues"
import * as issue_replies_schema from "~/schema/issue_replies"
import { storage_copy } from "~/storage.server"
import type { IssueForQueryWith } from "~/schema/query"
import arrays_equal from "~/utils/arrays_equal"

const { points } = points_schema

const db = drizzle(process.env.DATABASE_URL!, {
  schema: {
    ...projects_schema,
    ...users_schema,
    ...points_schema,
    ...issues_schema,
    ...issue_replies_schema,
  },
  // logger: true,
})

export async function get(id: Issue["id"]): Promise<IssueForQueryWith | null> {
  const issues_result = await db.query.issues.findMany({
    columns: {
      id: true,
      point_ids: true,
      description: true,
      attachments: true,
      ask_time: true,
      solution: true,
      solution_time: true,
      system: true,
      sub_company: true,
      location1: true,
      location2: true,
      area_des: true,
      responsible_party: true,
    },
    where: eq(model.id, id),
    with: {
      asker: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      solver: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      replies: {
        columns: {
          id: true,
          create_time: true,
          content: true,
          solution: true,
          responsible_party: true,
          solution_point_ids: true,
        },
        with: {
          creator: {
            columns: {
              id: true,
              role: true,
              name_chinese: true,
              disable: true,
            },
          },
        },
      },
    },
  })

  if (issues_result.length === 0) return null

  const points_result = await db.query.points.findMany({
    columns: {
      id: true,
      name: true,
      status: true,
      type: true,
      slug: true,
    },
    where: and(inArray(points.id, issues_result[0].point_ids)),
  })

  const { point_ids, ...result } = issues_result[0]
  return { ...result, points: points_result }
}

export const get_count = async (project_id: number, point_ids: number[]) =>
  await db
    .select({ count: count() })
    .from(model)
    .where(
      and(
        eq(model.project_id, project_id),
        point_ids.length ? arrayContains(model.point_ids, point_ids) : undefined,
      ),
    )
    .then((rows) => rows.at(0))

export async function find(
  project_id: number,
  point_ids: number[],
  description: string | undefined,
  with_replays: boolean,
  offset: number | undefined,
  limit: number | undefined,
): Promise<IssueForQueryWith[]> {
  const issues_result = await db.query.issues.findMany({
    columns: {
      id: true,
      point_ids: true,
      description: true,
      attachments: true,
      ask_time: true,
      solution: true,
      solution_time: true,
      system: true,
      sub_company: true,
      location1: true,
      location2: true,
      area_des: true,
      responsible_party: true,
    },
    where: and(
      eq(model.project_id, project_id),
      point_ids.length ? arrayContains(model.point_ids, point_ids) : undefined,
      description ? ilike(model.description, `%${description}%`) : undefined,
    ),
    with: {
      asker: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      solver: {
        columns: {
          id: true,
          role: true,
          name_chinese: true,
        },
      },
      replies: with_replays
        ? {
            columns: {
              id: true,
              create_time: true,
              content: true,
              solution: true,
              responsible_party: true,
              solution_point_ids: true,
            },
            with: {
              creator: {
                columns: {
                  id: true,
                  role: true,
                  name_chinese: true,
                  disable: true,
                },
              },
            },
          }
        : undefined,
    },
    limit,
    offset,
  })

  const all_point_ids = issues_result.map((item) => item.point_ids).flat()

  const points_result = await db.query.points.findMany({
    columns: {
      id: true,
      name: true,
      status: true,
      type: true,
      slug: true,
    },
    where: and(inArray(points.id, all_point_ids)),
  })

  return issues_result.map((issue) => {
    const { point_ids, ...result } = issue
    return {
      ...result,
      points: points_result.filter((p) => issue.point_ids.includes(p.id)),
    }
  })
}

export const insert = async (data: NewIssue): Promise<Issue> => {
  const { success, error } = issus_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  await restrict_point_belong_project(data.point_ids, data.project_id)

  const [inserted] = await db.insert(model).values(data).returning()

  if (data.attachments?.length) {
    await Promise.all(
      data.attachments.map(async (attachment) => {
        const source_key = `${data.project_id}/temp/${attachment}`
        const dest_key = `${data.project_id}/${inserted.id}/${attachment}`
        await storage_copy("issues-attachments", source_key, dest_key)
      }),
    )
  }

  return inserted
}

export const update = async (data: UpdateIssue): Promise<Issue> => {
  const { success, error } = issus_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const [preserved] = await db.select().from(model).where(eq(model.id, data.id))
  if (!preserved) throw new Response("not found", { status: Status.NotFound })

  await restrict_point_belong_project(data.point_ids, data.project_id, preserved)

  const { id, ...updated_data } = data
  const [updated] = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()

  if (data.attachments?.length) {
    await Promise.all(
      data.attachments.map(async (attachment) => {
        const source_key = `${data.project_id}/temp/${attachment}`
        const dest_key = `${data.project_id}/${updated.id}/${attachment}`
        await storage_copy("issues-attachments", source_key, dest_key)
      }),
    )
  }

  return updated
}

export const remove = async (id: Issue["id"]): Promise<void> => {
  await db.delete(model).where(eq(model.id, id))
}

export const restrict_point_belong_project = async (
  point_ids?: number[],
  project_id?: number,
  preserved?: Issue,
) => {
  if (preserved) {
    if (
      !(point_ids && arrays_equal(point_ids, preserved.point_ids)) &&
      !(project_id && project_id !== preserved.project_id)
    ) {
      return
    }

    point_ids = point_ids ?? preserved.point_ids
    project_id = project_id ?? preserved.project_id
  }

  for (const point_id of point_ids!) {
    const result = await db
      .select()
      .from(points)
      .where(and(eq(points.id, point_id), eq(points.project_id, project_id!)))

    if (result.length == 0) {
      throw new Response(`restrict_point_belong_project ${point_id}`, {
        status: Status.Conflict,
      })
    }
  }
}
