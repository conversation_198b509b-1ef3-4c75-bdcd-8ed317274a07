import "dotenv/config"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { links as model, link_zi, link_zu, type Link, type NewLink, type UpdateLink } from "~/schema/links"

const db = drizzle(process.env.DATABASE_URL!)

export const get = async (id: Link["id"]): Promise<Link | undefined> =>
  await db
    .select()
    .from(model)
    .where(eq(model.id, id))
    .then((rows) => rows.at(0))

export const find = async (
  project_id: number,
  line_submission_id?: number,
  device_submission_id?: number,
  parent_device_submission_id?: number,
  port_id?: number,
  parent_port_id?: number,
) =>
  await db
    .select()
    .from(model)
    .where(
      and(
        eq(model.project_id, project_id),
        line_submission_id ? eq(model.line_submiting_id, line_submission_id) : undefined,
        device_submission_id ? eq(model.device_submiting_id, device_submission_id) : undefined,
        parent_device_submission_id
          ? eq(model.parent_device_submiting_id, parent_device_submission_id)
          : undefined,
        port_id ? eq(model.port_id, port_id) : undefined,
        parent_port_id ? eq(model.parent_port_id, parent_port_id) : undefined,
      ),
    )

export async function insert(data: NewLink): Promise<Link> {
  const { success, error } = link_zi.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const result = await db.insert(model).values(data).returning()
  return result[0]
}

export async function update(data: UpdateLink) {
  const { success, error } = link_zu.safeParse(data)
  if (!success) {
    throw new Response(`${error}`, { status: Status.BadRequest })
  }

  const preserved_ = await db.select().from(model).where(eq(model.id, data.id))
  if (preserved_.length === 0) throw new Response("not found", { status: Status.NotFound })

  const { id, ...updated_data } = data
  const result = await db.update(model).set(updated_data).where(eq(model.id, id)).returning()
  return result[0]
}

export async function remove(id: Link["id"]) {
  await db.delete(model).where(eq(model.id, id))
}
