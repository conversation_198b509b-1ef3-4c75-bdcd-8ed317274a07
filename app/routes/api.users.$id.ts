import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update } from "~/models/users.server"
import { role_z, type UpdateUser, type User } from "~/schema/users"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ params, context }: LoaderFunctionArgs) => {
  const user = context.user as User
  if (user.role !== role_z.enum.admin) {
    throw new Response("not allowed", { status: Status.Forbidden })
  }

  invariant(params.id, "missing id")

  const item = await get(parseInt(params.id))
  if (!item) throw new Response("not found", { status: Status.NotFound })

  return Response.json(item)
})

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  const user = context.user as User
  if (user.role !== role_z.enum.admin) {
    throw new Response("not allowed", { status: Status.Forbidden })
  }

  if (request.method === Method.PUT) {
    const data: UpdateUser = await request.json()

    const item = await update(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
