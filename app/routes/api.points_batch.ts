import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert_batch, type NewPointForBatch, update_batch } from "~/models/points.batch.server"
import Handle from "~/utils/handle_request"
import { type Point } from "~/schema/points"
import { type User } from "~/schema/users"
import { cleanCachified } from "~/utils/cache"

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewPointForBatch[] = await request.json()

    const item = await insert_batch(data)
    cleanCachified("points")

    return Response.json(item)
  } else if (request.method === Method.PUT) {
    const data: Point[] = await request.json()

    const item = await update_batch(data, context.user as User)
    cleanCachified("points", "points.$id")

    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
