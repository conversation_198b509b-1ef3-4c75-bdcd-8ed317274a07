import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update } from "~/models/forms.combine.server"
import { type UpdateFormCombine } from "~/schema/forms.combine"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ params, request }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const { searchParams } = new URL(request.url)
  const update_time_start = searchParams.get("update_time_start") ?? undefined
  const update_time_end = searchParams.get("update_time_end") ?? undefined
  const plan_time_start = searchParams.get("plan_time_start") ?? undefined
  const plan_time_end = searchParams.get("plan_time_end") ?? undefined

  const item = await get(
    parseInt(params.id),
    update_time_start,
    update_time_end,
    plan_time_start,
    plan_time_end,
  )
  return Response.json(item)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateFormCombine = await request.json()

    const item = await update(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
