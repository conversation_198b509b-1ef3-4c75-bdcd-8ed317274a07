import type { ActionFunctionArgs } from "@remix-run/node"
import path from "path"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()
    const image_file = formData.get("image") as File
    if (!image_file) throw new Response("image missing", { status: Status.BadRequest })

    const user_id = formData.get("user_id")
    if (!user_id) throw new Response("user_id missing", { status: Status.BadRequest })

    const key = `${user_id}.jpg`

    await storage_put("users", image_file, key)
    return new Response(key, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
