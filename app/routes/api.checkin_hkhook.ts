import type { ActionFunctionArgs } from "@remix-run/node"
import dayjs from "dayjs"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert as insert_record } from "~/models/checkins.server"

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const content_type = request.headers.get("Content-Type")
    console.log("checkin_hkhook content_type", content_type)

    if (content_type?.includes("multipart/form-data")) {
      const form = await request.formData()
      // console.log("workers_hkhook form", form)
      const data = form.get("AccessControllerEvent")
      if (!data) throw new Error("missing AccessControllerEvent")
      const json: HKHookResponse = JSON.parse(data as string)
      console.log("checkin_hkhook json", json)

      const device_id = json.shortSerialNumber
      if (!device_id) {
        return new Response("missing shortSerialNumber", { status: Status.BadRequest })
      }
      const identity = json.AccessControllerEvent?.employeeNoString
      if (!identity) {
        return new Response("missing employeeNoString", { status: Status.NotAcceptable })
      }
      await insert_record(
        {
          device_id,
          create_time: dayjs().toISOString(),
        },
        identity,
      )
      console.log("checkin_hkhook record", identity, device_id)

      return new Response("ok", { status: Status.Ok })
    }

    // const text = await request.text()
    // console.log("workers_hkhook text", text)
    return new Response("ignore", { status: Status.NotAcceptable })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
}

interface HKHookResponse {
  ipAddress: string
  ipv6Address?: string
  portNo: number
  protocol: string
  macAddress: string
  channelID: number
  dateTime: string
  activePostCount: number
  eventType: string
  eventState: string
  eventDescription: string
  shortSerialNumber?: string
  AccessControllerEvent?: AccessControllerEvent
}

interface AccessControllerEvent {
  deviceName: string
  majorEventType: number
  subEventType: number
  serialNo: number
  frontSerialNo: number
  label: string
  purePwdVerifyEnable: boolean

  name?: string
  cardReaderNo?: number
  employeeNoString?: string
  userType?: string
  currentVerifyMode?: string
  mask?: string
  helmet?: string
  picturesNumber?: number
  FaceRect?: {
    height: number
    width: number
    x: number
    y: number
  }

  doorNo?: number
}
