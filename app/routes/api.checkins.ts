import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/checkins.server"
import { type NewCheckIn } from "~/schema/checkins"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id")
  const user_id = searchParams.parse_integer("user_id")
  const device = searchParams.get("device") ?? undefined
  const time_start = searchParams.get("time_start") ?? undefined
  const time_end = searchParams.get("time_end") ?? undefined

  const result = await find(project_id, user_id, device, time_start, time_end)
  return Response.json(result)
})
