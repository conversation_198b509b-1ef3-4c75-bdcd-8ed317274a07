import type { LoaderFunctionArgs } from "@remix-run/node"

import { get_count } from "~/models/cabinets.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const point_id = searchParams.parse_integer("point_id")
  const material_id = searchParams.parse_integer("material_id")
  const room_code_group = searchParams.parse_boolean("room_code_group")

  const result = await get_count(project_id, point_id, material_id, room_code_group)
  return Response.json(result)
})
