import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update, remove } from "~/models/projects.server"
import { type UpdateProject } from "~/schema/projects"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ params }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const project = await get(parseInt(params.id))
  return Response.json(project)
})

export const action = Handle(async ({ request, params }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateProject = await request.json()

    const project = await update(data)
    return Response.json(project)
  } else if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")
    await remove(parseInt(params.id))
    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
