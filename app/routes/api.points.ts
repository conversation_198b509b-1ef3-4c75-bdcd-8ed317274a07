import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/points.server"
import { type NewPoint, point_status_z } from "~/schema/points"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { point_type_z } from "~/schema/point_types"
import { cleanCachified } from "~/utils/cache"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id", true) as number
    const point_type = searchParams.parse_zod("point_type", point_type_z)
    const status = searchParams.parse_zod("status", point_status_z)
    const name = searchParams.get("name") ?? undefined
    const offset = searchParams.parse_integer("offset")
    const limit = searchParams.parse_integer("limit")

    const result = await find(project_id, point_type, status, name, offset, limit)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewPoint = await request.json()
    const item = await insert(data)
    cleanCachified("points")

    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
