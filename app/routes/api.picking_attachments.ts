import type { ActionFunctionArgs } from "@remix-run/node"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()

    const file = formData.get("file") as File
    if (!file) throw new Response("file missing", { status: Status.BadRequest })

    const picking_id = formData.get("picking_id")
    if (!picking_id) throw new Response("picking_id missing", { status: Status.BadRequest })
    const slug = formData.get("slug")
    if (!slug) throw new Response("slug missing", { status: Status.BadRequest })

    const key = `${picking_id}/${slug}_${file.name}`

    await storage_put("picking-attachments", file, key)
    return new Response(key, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
