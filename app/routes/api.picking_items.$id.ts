import type { ActionFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { removeItem } from "~/models/pickings.server"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request, params }: ActionFunctionArgs) => {
  if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")

    await removeItem(parseInt(params.id))
    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
