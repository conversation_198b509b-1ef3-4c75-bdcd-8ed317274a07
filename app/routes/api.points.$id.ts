import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update, remove } from "~/models/points.server"
import { type UpdatePoint } from "~/schema/points"
import Handle from "~/utils/handle_request"
import { cleanCachified } from "~/utils/cache"
import { type User } from "~/schema/users"

export const loader = Handle(async ({ params }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const item = await get(parseInt(params.id))
  return Response.json(item)
})

export const action = Handle(async ({ request, params, context }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdatePoint = await request.json()

    const item = await update(data, context.user as User)
    cleanCachified("points", "points.$id")

    return Response.json(item)
  } else if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")
    await remove(parseInt(params.id))
    cleanCachified("points", "points.$id")

    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
