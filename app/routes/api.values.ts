// import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

// import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
// import { find, insert } from "~/models/values.server"
// import { type NewValue } from "~/schema/values"
// import { URLSearchParamsExtended } from "~/utils/params_parse"
// import Handle from "~/utils/handle_request"

// export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
//   const searchParams = new URLSearchParamsExtended(request.url)
//   const submit_id = searchParams.parse_integer("submit_id", true) as number
//   const unit_id = searchParams.parse_integer("unit_id", true) as number

//   const result = await find(submit_id, unit_id)
//   return Response.json(result)
// })

// export const action = Handle(async ({ request }: ActionFunctionArgs) => {
//   if (request.method === Method.POST) {
//     const data: NewValue = await request.json()
//     const item = await insert(data)
//     return Response.json(item)
//   }

//   return new Response("not allowed", { status: Status.MethodNotAllowed })
// })
