import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"

import { role_z, type User, type AddUser } from "~/schema/users"
import { find, insert } from "~/models/users.server"
import Handle from "~/utils/handle_request"
import { URLSearchParamsExtended } from "~/utils/params_parse"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const name = searchParams.get("name_chinese") ?? undefined
  const attendance = searchParams.parse_boolean("attendance")
  const account_id = searchParams.parse_integer("account_id")

  const result = await find(name, account_id, attendance)
  return Response.json(result)
})

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  const user = context.user as User
  if (user.role !== role_z.enum.admin) {
    throw new Response("not allowed", { status: Status.Forbidden })
  }

  if (request.method === Method.POST) {
    const data: AddUser = await request.json()
    const project = await insert(data)
    return Response.json(project)
  }

  return new Response("Not Allowed", { status: Status.MethodNotAllowed })
})
