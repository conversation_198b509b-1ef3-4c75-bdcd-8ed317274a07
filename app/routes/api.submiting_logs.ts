import { type LoaderFunctionArgs } from "@remix-run/node"

import { getLogs } from "~/models/submissions.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const submission_id = searchParams.parse_integer("submiting_id", true) as number

    const result = await getLogs(submission_id)
    return Response.json(result)
  },
  { auth: true, cachified: "submission" },
)
