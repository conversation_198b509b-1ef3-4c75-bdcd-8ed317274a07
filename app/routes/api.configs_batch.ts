import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert_batch } from "~/models/configs.server"
import { type NewConfig } from "~/schema/configs"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewConfig[] = await request.json()
    const item = await insert_batch(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
