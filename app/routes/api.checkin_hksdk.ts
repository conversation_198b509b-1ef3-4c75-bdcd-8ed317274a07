import type { ActionFunctionArgs } from "@remix-run/node"
import dayjs from "dayjs"
import { XMLParser } from "fast-xml-parser"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert as insert_record } from "~/models/checkins.server"
import { storage_put } from "~/storage.server"

const parser = new XMLParser()

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const content_type = request.headers.get("Content-Type")
    console.log("checkin_hksdk content_type", content_type)

    if (content_type?.includes("text/plain")) {
      const text = await request.text()
      // console.log("workers_hksdk text", text)

      let xml = parser.parse(text)
      console.log("checkin_hksdk xml", xml)
      const params = xml.PPVSPMessage.Params
      const device_id = params.DeviceID
      if (!device_id) {
        return new Response("missing deviceID", { status: Status.BadRequest })
      }
      const identity = params.employeeNoString?.toString()
      if (!identity) {
        return new Response("missing employeeNoString", { status: Status.NotAcceptable })
      }

      const now = dayjs()
      const record = await insert_record(
        {
          device_id,
          create_time: now.toISOString(),
        },
        identity,
      )
      console.log("checkin_hksdk record", identity, device_id)

      const picUrl = params.PicDataUrl
      if (picUrl) {
        const fetchUrl = "http://127.0.0.1:6011" + picUrl
        console.log("workers_hkhook fetchUrl", fetchUrl)
        const resp = await fetch("http://127.0.0.1:6011" + picUrl)
        if (resp.ok) {
          const imageBuffer = await resp.arrayBuffer()
          const key = `${now.format("YYYYMMDD")}/${record.id}.jpeg`
          const imageFile = new File([imageBuffer], `${record.id}.jpeg`, { type: "image/jpeg" })
          await storage_put("checkins", imageFile, key)
          console.log("checkin image done", picUrl)
        } else {
          console.error("checkin image fetch fail.", resp.status, resp.statusText, picUrl)
        }
      } else {
        console.warn("picUrl null, skipping save checkin image.")
      }

      return new Response("ok", { status: Status.Ok })
    }

    // const text = await request.text()
    // console.log("workers_hkhook text", text)
    return new Response("ignore", { status: Status.NotAcceptable })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
}

interface HKHookResponse {
  ipAddress: string
  ipv6Address?: string
  portNo: number
  protocol: string
  macAddress: string
  channelID: number
  dateTime: string
  activePostCount: number
  eventType: string
  eventState: string
  eventDescription: string
  shortSerialNumber?: string
  AccessControllerEvent?: AccessControllerEvent
}

interface AccessControllerEvent {
  deviceName: string
  majorEventType: number
  subEventType: number
  serialNo: number
  frontSerialNo: number
  label: string
  purePwdVerifyEnable: boolean

  name?: string
  cardReaderNo?: number
  employeeNoString?: string
  userType?: string
  currentVerifyMode?: string
  mask?: string
  helmet?: string
  picturesNumber?: number
  FaceRect?: {
    height: number
    width: number
    x: number
    y: number
  }

  doorNo?: number
}
