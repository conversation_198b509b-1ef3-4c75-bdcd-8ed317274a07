import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update, remove } from "~/models/orders.server"
import { type UpdateOrder } from "~/schema/orders"
import { type User } from "~/schema/users"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ params }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const item = await get(parseInt(params.id))
  return Response.json(item)
})

export const action = Handle(async ({ request, params, context }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateOrder = await request.json()

    const item = await update(data)
    return Response.json(item)
  } else if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")
    const user = context.user as User
    await remove(parseInt(params.id), user)
    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
