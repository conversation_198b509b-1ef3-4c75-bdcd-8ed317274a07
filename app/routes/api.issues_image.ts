import type { ActionFunctionArgs } from "@remix-run/node"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()

    const image_file = formData.get("image") as File
    if (!image_file) throw new Response("image missing", { status: Status.BadRequest })

    const project_id = formData.get("project_id")
    if (!project_id) throw new Response("project_id missing", { status: Status.BadRequest })
    const slug = formData.get("slug")
    if (!slug) throw new Response("slug missing", { status: Status.BadRequest })

    const key = `${project_id}/temp/${slug}_${image_file.name}`

    await storage_put("issues-attachments", image_file, key)
    return new Response(`${slug}_${image_file.name}`, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
