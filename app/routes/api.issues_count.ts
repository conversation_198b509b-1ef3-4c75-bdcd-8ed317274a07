import type { LoaderFunctionArgs } from "@remix-run/node"

import { get_count } from "~/models/issues.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const point_ids = searchParams.parse_integer_array("point_ids")

  const result = await get_count(project_id, point_ids)
  return Response.json(result)
})
