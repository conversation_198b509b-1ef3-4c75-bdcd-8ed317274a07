import type { LoaderFunctionArgs } from "@remix-run/node"

import { getNames } from "~/models/points.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id", true) as number

    const result = await getNames(project_id)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)
