import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update, remove } from "~/models/materials.server"
import { type UpdateMaterial } from "~/schema/materials"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ params }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const item = await get(parseInt(params.id))
  return Response.json(item)
})

export const action = Handle(async ({ request, params }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateMaterial = await request.json()

    const item = await update(data)
    return Response.json(item)
  } else if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")
    await remove(parseInt(params.id))
    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
