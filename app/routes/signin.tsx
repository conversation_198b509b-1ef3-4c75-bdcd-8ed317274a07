import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import { Form, useLoaderData } from "@remix-run/react"

import { auth, sessionStorage } from "~/auth.form.server"

export const action = async ({ request }: ActionFunctionArgs) => {
  await auth.authenticate("form", request, {
    successRedirect: "/projects",
    failureRedirect: "/signin",
  })
}

interface LoaderError {
  readonly message: string
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await auth.isAuthenticated(request, { successRedirect: "/projects" })
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  const error = session.get(auth.sessionErrorKey) as LoaderError | null
  return Response.json({ error })
}

export default function SignIn() {
  const { error } = useLoaderData<typeof loader>()

  return (
    <>
      <pre>signin</pre>
      <Form method="post">
        {error && <div>{error.message}</div>}
        <div>
          <label htmlFor="name">Username</label>
          <input type="text" name="name" id="name" defaultValue="czmp" />
        </div>

        <div>
          <label htmlFor="password">Password</label>
          <input type="password" name="password" id="password" defaultValue="test" />
        </div>

        <button type="submit">Log In</button>
      </Form>
    </>
  )
}
