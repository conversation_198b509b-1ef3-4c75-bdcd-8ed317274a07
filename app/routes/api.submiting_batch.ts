import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert_batch, update_batch, remove_batch } from "~/models/submissions.server"
import { type UpdateSubmission, type NewSubmission, Submission } from "~/schema/submissions"
import Handle from "~/utils/handle_request"
import { User } from "~/schema/users"
import { cleanCachified } from "~/utils/cache"

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data_batch: NewSubmission[] = await request.json()

    const user = context.user as User
    for (const data of data_batch) {
      data.user_id = user.id
    }

    const project = await insert_batch(data_batch)
    cleanCachified("submission", "submission.$id")

    return Response.json(project)
  } else if (request.method === Method.PUT) {
    const data_batch: UpdateSubmission[] = await request.json()

    const user = context.user as User
    for (const data of data_batch) {
      data.user_id = user.id
    }

    const results = await update_batch(data_batch, user)
    cleanCachified("submission", "submission.$id")

    return Response.json(results)
  } else if (request.method === Method.DELETE) {
    const ids: number[] = await request.json()

    await remove_batch(ids)
    cleanCachified("submission", "submission.$id")

    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
