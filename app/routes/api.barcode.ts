import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"
import transHost from "~/utils/trans_host"
import { detectCodes, extractCodeBuffers } from "~/utils/roboflow"
import { decodeImage } from "~/utils/zxing"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method !== Method.POST) {
    return new Response("not allowed", { status: Status.MethodNotAllowed })
  }

  const formData = await request.formData()
  const imageUrl = formData.get("image_url") as string | null
  let imageArrayBuffer: ArrayBuffer

  if (imageUrl) {
    const image = await fetch(transHost(imageUrl))
    if (!image.ok) throw new Response(`image not found ${imageUrl}`, { status: Status.NotFound })
    imageArrayBuffer = await image.arrayBuffer()
  } else {
    const image = formData.get("image") as File
    if (!image) throw new Response("image missing", { status: Status.BadRequest })
    imageArrayBuffer = await image.arrayBuffer()
  }

  const imageBuffer = Buffer.from(imageArrayBuffer)
  const imageBase64 = imageBuffer.toString("base64")

  const barModel = formData.get("bar_model") as string | null
  const qrModel = formData.get("qr_model") as string | null
  const [barDetections, qrDetections] = await Promise.all([
    detectCodes(imageBase64, barModel ?? "barcode-detection-mziov/2"),
    detectCodes(imageBase64, qrModel ?? "qr-scanner/2140"),
  ])

  const [barBuffers, qrBuffers] = await Promise.all([
    extractCodeBuffers(imageBuffer, barDetections),
    extractCodeBuffers(imageBuffer, qrDetections),
  ])

  const [barcodes, qrcodes] = await Promise.all([
    Promise.all(barBuffers.map(decodeImage)),
    Promise.all(qrBuffers.map(decodeImage)),
  ])

  return Response.json({
    barcodes,
    qrcodes,
    barcodePredictions: barDetections.predictions,
    qrcodePredictions: qrDetections.predictions,
  })
})
