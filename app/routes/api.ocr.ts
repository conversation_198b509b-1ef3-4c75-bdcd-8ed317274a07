import type { ActionFunctionArgs } from "@remix-run/node"
import { createWorker } from "tesseract.js"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"
// import ali_ocr from "~/utils/ali-ocr"

// const ocrAliApi = `http://127.0.0.1:8270/ocr`
const ocrAliApi = `http://218.93.190.116:8270/ocr`

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()

    const aliyun = formData.get("aliyun") === "true"
    if (aliyun) {
      const image_url = formData.get("image_url") as string | null
      if (!image_url) throw new Response("image_url missing", { status: Status.BadRequest })

      // const ret = await ali_ocr(image_url)
      // return Response.json(ret)
      const resp = await fetch(`${ocrAliApi}?url=${encodeURIComponent(image_url)}`)
      const result = await resp.json()
      return new Response(result.content, { status: Status.Ok })
    }

    const chinese = formData.get("chinese") === "true"
    const worker = await createWorker(chinese ? ["eng", "chi_sim"] : ["eng"])

    let ret: Tesseract.RecognizeResult
    const image_url = formData.get("image_url") as string | null
    if (image_url) {
      ret = await worker.recognize(image_url)
    } else {
      const image_file = formData.get("image") as File
      if (!image_file) throw new Response("image missing", { status: Status.BadRequest })
      console.log(image_file)
      const imageArrayBuffer = await image_file.arrayBuffer()
      // console.log(`image ArrayBuffer size: ${imageArrayBuffer.byteLength}`)
      ret = await worker.recognize(Buffer.from(imageArrayBuffer))
    }

    console.log(ret.data.text)
    await worker.terminate()

    return new Response(ret.data.text, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
