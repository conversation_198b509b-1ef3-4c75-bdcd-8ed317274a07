import type { LoaderFunctionArgs } from "@remix-run/node"

import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { find, find_prepare } from "~/models/points.query.whole.server"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id", true) as number

    const result = await find_prepare(project_id)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)
