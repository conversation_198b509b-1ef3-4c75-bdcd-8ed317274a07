import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/pickings.server"
import { type PickingWith, document_type_z } from "~/schema/pickings"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const creator_id = searchParams.parse_integer("creator_id")
  const receiver_id = searchParams.parse_integer("receiver_id")
  const with_items = searchParams.parse_boolean("with_items")
  const type = searchParams.parse_zod("type", document_type_z)
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(project_id, creator_id, receiver_id, type, with_items, offset, limit)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: PickingWith = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
