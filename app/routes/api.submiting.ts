import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/submissions.server"
import { type NewSubmission, submission_status_z, SubmissionStatus } from "~/schema/submissions"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { User } from "~/schema/users"
import invariant from "tiny-invariant"
import { cleanCachified } from "~/utils/cache"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id")
    const form_ids = searchParams.parse_integer_array("form_ids")
    const point_id = searchParams.parse_integer("point_id")
    const user_id = searchParams.parse_integer("user_id")
    const material_id = searchParams.parse_integer("material_id")
    const cabinet_material_id = searchParams.parse_integer("cabinet_material_id")
    const done = searchParams.parse_boolean("done")
    const done_user_id = searchParams.parse_integer("done_user_id")
    // const status = searchParams.parse_zod("status", submitting_statusz)
    const statuses = searchParams.parse_zods("statuses", submission_status_z)

    const update_time_start = searchParams.get("update_time_start") ?? undefined
    const update_time_end = searchParams.get("update_time_end") ?? undefined
    const plan_time_start = searchParams.get("plan_time_start") ?? undefined
    const plan_time_end = searchParams.get("plan_time_end") ?? undefined

    const result = await find(
      form_ids,
      project_id,
      point_id,
      user_id,
      material_id,
      cabinet_material_id,
      done,
      done_user_id,
      statuses,
      update_time_start,
      update_time_end,
      plan_time_start,
      plan_time_end,
    )
    return Response.json(result)
  },
  { auth: true, cachified: "submission" },
)

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewSubmission = await request.json()
    invariant(data, "missing data")

    const user = context.user as User
    data.user_id = user.id

    const project = await insert(data)
    cleanCachified("submission")

    return Response.json(project)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
