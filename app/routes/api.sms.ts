import type { ActionFunctionArgs } from "@remix-run/node"
import { HttpStatusCode as Status } from "~/utils/http_code"
import sms from "~/utils/sms"

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method === "POST") {
    const { text, phone, template } = await request.json()
    const result = await sms(template, text, phone)
    return new Response(result.toString(), { status: Status.Ok })
  }

  return new Response("NotAllowed", { status: Status.MethodNotAllowed })
}
