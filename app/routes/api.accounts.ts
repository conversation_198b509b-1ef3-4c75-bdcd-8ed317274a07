import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node"

import { find, signUp } from "~/models/accounts.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { type NewAccount } from "../schema/accounts"
import Handle from "~/utils/handle_request"
import { URLSearchParamsExtended } from "~/utils/params_parse"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const name = searchParams.get("name") ?? undefined
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(name, offset, limit)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewAccount = await request.json()
    const item = await signUp(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
