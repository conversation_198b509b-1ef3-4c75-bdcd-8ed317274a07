import type { ActionFunctionArgs } from "@remix-run/node"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()

    const image_file = formData.get("image") as File
    if (!image_file) throw new Response("image missing", { status: Status.BadRequest })

    const order_id = formData.get("order_id")
    if (!order_id) throw new Response("order_id missing", { status: Status.BadRequest })
    const slug = formData.get("slug")
    if (!slug) throw new Response("slug missing", { status: Status.BadRequest })

    const key = `${order_id}/${slug}_${image_file.name}`

    await storage_put("order-reply-images", image_file, key)
    return new Response(key, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
