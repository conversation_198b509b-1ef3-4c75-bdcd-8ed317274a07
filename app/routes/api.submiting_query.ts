import type { LoaderFunctionArgs } from "@remix-run/node"

import { find_prepare } from "~/models/submissions.query.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const form_id = searchParams.parse_integer("form_id")
    const point_id = searchParams.parse_integer("point_id")
    const user_id = searchParams.parse_integer("user_id")
    const material_id = searchParams.parse_integer("material_id")
    const cabinet_material_id = searchParams.parse_integer("cabinet_material_id")
    const done = searchParams.parse_boolean("done")
    const offset = searchParams.parse_integer("offset")
    const limit = searchParams.parse_integer("limit")
    // const orders = searchParams.parse_orders()

    const result = await find_prepare(
      form_id,
      point_id,
      user_id,
      material_id,
      cabinet_material_id,
      done,
      offset,
      limit,
    ) // , orders)
    return Response.json(result)
  },
  { auth: true, cachified: "submission" },
)
