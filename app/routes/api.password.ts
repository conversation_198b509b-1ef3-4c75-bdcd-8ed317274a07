import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { updatePassword } from "~/models/accounts.server"
import { type Account } from "~/schema/accounts"
import { role_z, type User } from "~/schema/users"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const formData: FormData = await request.formData()
    const account_id = formData.get("account_id") as string | null
    const password_raw = formData.get("password") as string | null
    if (!password_raw) {
      throw new Response("missing password", { status: Status.BadRequest })
    }

    if (account_id) {
      const user = context.user as User
      if (user.role !== role_z.enum.admin) throw new Response("not allowed", { status: Status.Forbidden })
      await updatePassword(parseInt(account_id), password_raw)
    } else {
      const account = context.account as Account
      await updatePassword(account.id, password_raw)
    }

    return new Response("ok", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
