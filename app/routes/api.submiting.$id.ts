import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update, remove } from "~/models/submissions.server"
import { type UpdateSubmission } from "~/schema/submissions"
import Handle from "~/utils/handle_request"
import { User } from "~/schema/users"
import { cleanCachified } from "~/utils/cache"

export const loader = Handle(
  async ({ params }: LoaderFunctionArgs) => {
    invariant(params.id, "missing id")

    const item = await get(parseInt(params.id))
    return Response.json(item)
  },
  { auth: true, cachified: "submission.$id" },
)

export const action = Handle(async ({ request, params, context }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateSubmission = await request.json()

    const user = context.user as User
    data.user_id = user.id

    const item = await update(data, user)
    cleanCachified("submission", "submission.$id")

    return Response.json(item)
  } else if (request.method === Method.DELETE) {
    invariant(params.id, "missing id")
    await remove(parseInt(params.id))
    cleanCachified("submission", "submission.$id")

    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
