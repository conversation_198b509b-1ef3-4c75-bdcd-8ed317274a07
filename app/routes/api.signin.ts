import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { auth, sessionStorage } from "~/auth.form.server"
import { account_zi } from "~/schema/accounts"
import Handle from "~/utils/handle_request"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    const form = await request.clone().formData()
    const name = form.get("name") as string | null
    const password = form.get("password") as string | null

    const { success, error } = account_zi.safeParse({ name, password })
    if (!success) {
      throw new Response(`${error}`, { status: Status.BadRequest })
    }

    const user = await auth.authenticate("form", request)

    // https://github.com/sergiodxa/remix-auth?tab=readme-ov-file#usage
    const session = await sessionStorage.getSession(request.headers.get("cookie"))
    session.set("user", user)

    return Response.json(user, { headers: { "Set-Cookie": await sessionStorage.commitSession(session) } })
  },
  { auth: false },
)
