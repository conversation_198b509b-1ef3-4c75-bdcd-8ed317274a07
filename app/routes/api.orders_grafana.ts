import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { type GrafanaAlert, insertFromGrafana } from "~/models/orders.server"
import Handle from "~/utils/handle_request"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    if (request.method === Method.POST) {
      const data: { alerts: GrafanaAlert[] } = await request.json()
      console.log("Received Grafana alerts:", data.alerts)
      const results = await insertFromGrafana(data.alerts)
      return Response.json(results)
    }
    return new Response("not allowed", { status: Status.MethodNotAllowed })
  },
  { auth: false },
)
