import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/links.server"
import { type NewLink } from "~/schema/links"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const line_submission_id = searchParams.parse_integer("line_submiting_id")
  const device_submission_id = searchParams.parse_integer("device_submiting_id")
  const parent_device_submission_id = searchParams.parse_integer("parent_device_submiting_id")
  const port_id = searchParams.parse_integer("port_id")
  const parent_port_id = searchParams.parse_integer("parent_port_id")

  const result = await find(
    project_id,
    line_submission_id,
    device_submission_id,
    parent_device_submission_id,
    port_id,
    parent_port_id,
  )
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewLink = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
