import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/cabinets.server"
import { type NewCabinet } from "~/schema/cabinets"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const point_id = searchParams.parse_integer("point_id")
  const material_id = searchParams.parse_integer("material_id")
  const code = searchParams.get("code") ?? undefined
  const room_code = searchParams.get("room_code") ?? undefined
  const type = searchParams.get("type") ?? undefined
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(project_id, point_id, material_id, code, room_code, type, offset, limit)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewCabinet = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
