import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node"
import Handle from "~/utils/handle_request"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { get, update } from "~/models/accounts.server"
import invariant from "tiny-invariant"
import { UpdateAccount } from "~/schema/accounts"

export const loader = Handle(async ({ params }: LoaderFunctionArgs) => {
  invariant(params.id, "missing id")

  const account = await get(parseInt(params.id, 10))
  if (!account) throw new Response("not found", { status: Status.NotFound })

  return Response.json(account)
})

export const action = Handle(async ({ request, params }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    const data: UpdateAccount = await request.json()

    const account = await update(data)
    return Response.json(account)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
