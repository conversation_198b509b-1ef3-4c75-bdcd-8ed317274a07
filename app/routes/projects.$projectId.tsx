import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import { useParams, redirect, useLoaderData } from "@remix-run/react"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { auth } from "~/auth.form.server"
import { get, update, remove } from "~/models/projects.server"
import type { Project } from "~/schema/projects"

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await auth.isAuthenticated(request, { failureRedirect: "/signin" })
  invariant(params.projectId, "missing projectId")

  const project = await get(parseInt(params.projectId, 10))
  return Response.json(project)
}

export const action = async ({ request, params }: ActionFunctionArgs) => {
  await auth.isAuthenticated(request, { failureRedirect: "/signin" })

  switch (request.method) {
    case Method.PUT: {
      const data: Project = await request.json()
      const project = await update(data)
      return Response.json(project)
    }
    case Method.DELETE: {
      invariant(params.projectId, "missing projectId")
      await remove(parseInt(params.projectId, 10))
      return redirect("/projects")
    }
    default:
      return new Response("Method not allowed", { status: Status.MethodNotAllowed })
  }
}

export default function Project() {
  const { projectId } = useParams()
  console.log(`projectId ${projectId}`)

  const data = useLoaderData<typeof loader>()
  return <pre>{JSON.stringify(data, null, 2)}</pre>
}
