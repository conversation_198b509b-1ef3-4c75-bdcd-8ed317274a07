import type { ActionFunctionArgs } from "@remix-run/node"
import Handle from "~/utils/handle_request"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { update_using, get } from "~/models/users.server"
import invariant from "tiny-invariant"
import { role_z, type User } from "~/schema/users"
import { type Account } from "~/schema/accounts"

export const action = Handle(async ({ request, params, context }: ActionFunctionArgs) => {
  if (request.method === Method.PUT) {
    invariant(params.id, "missing id")
    const user_id = parseInt(params.id, 10)
    const target_user = await get(user_id)
    if (!target_user) throw new Response("not found", { status: Status.NotFound })

    const account = context.account as Account
    const user = context.user as User
    if (target_user.account_id !== account.id && user.role !== role_z.enum.admin)
      throw new Response("not allowed", { status: Status.Forbidden })

    const item = await update_using(user_id)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
