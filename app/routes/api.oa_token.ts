import type { LoaderFunctionArgs } from "@remix-run/node"
import { role_z, User } from "~/schema/users"
import Handle from "~/utils/handle_request"
import { HttpStatusCode as Status } from "~/utils/http_code"

export const loader = Handle(async ({ context }: LoaderFunctionArgs) => {
  const user = context.user as User
  if (user.role !== role_z.enum.admin && user.role !== role_z.enum.manager) {
    throw new Response("not allowed", { status: Status.Forbidden })
  }

  return "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyIjoicHJvY2VkdXJlLWZvcm1zIiwiaWF0IjoxNzM2MzIyMTA0LCJleHAiOjE4MjI3MjIxMDR9.aXZM2UaLHnxmb_9IxeVSmnKyuK8RSjgOWVR36VN2n5E"
})
