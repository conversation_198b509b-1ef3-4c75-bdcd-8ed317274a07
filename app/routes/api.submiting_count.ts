import type { LoaderFunctionArgs } from "@remix-run/node"

import { get_count } from "~/models/submissions.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { submission_status_z } from "~/schema/submissions"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id")
    const form_ids = searchParams.parse_integer_array("form_ids")
    const point_id = searchParams.parse_integer("point_id")
    const user_id = searchParams.parse_integer("user_id")
    const material_id = searchParams.parse_integer("material_id")
    const cabinet_material_id = searchParams.parse_integer("cabinet_material_id")
    const done = searchParams.parse_boolean("done")
    const done_user_id = searchParams.parse_integer("done_user_id")
    const statuses = searchParams.parse_zods("status", submission_status_z)

    const update_time_start = searchParams.get("update_time_start") ?? undefined
    const update_time_end = searchParams.get("update_time_end") ?? undefined

    const result = await get_count(
      form_ids,
      project_id,
      point_id,
      user_id,
      material_id,
      cabinet_material_id,
      done,
      done_user_id,
      statuses,
      update_time_start,
      update_time_end,
    )
    return Response.json(result)
  },
  { auth: true, cachified: "submission" },
)
