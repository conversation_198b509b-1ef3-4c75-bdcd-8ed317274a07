import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/orders.server"
import { order_status_z, type New<PERSON>rder } from "~/schema/orders"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { type User } from "~/schema/users"

export const loader = Handle(async ({ request, context }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)

  const project_id = searchParams.parse_integer("project_id")
  const point_id = searchParams.parse_integer("point_id")
  const self = searchParams.parse_boolean("self")

  const creator_id = searchParams.parse_integer("creator_id")
  const handler_id = searchParams.parse_integer("handler_id")
  const order_status = searchParams.parse_zod("order_status", order_status_z)
  const title = searchParams.get("title") ?? undefined

  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(
    self ? (context.user as User).id : undefined,
    project_id,
    point_id,
    creator_id,
    handler_id,
    order_status,
    title,
    offset,
    limit,
  )
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewOrder = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
