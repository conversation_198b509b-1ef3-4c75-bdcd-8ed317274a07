import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert, update } from "~/models/material_allocats.server"
import type { NewMaterialAllocat, UpdateMaterialAllocat } from "~/schema/material_allocats"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id")
  const material_id = searchParams.parse_integer("material_id")
  const point_id = searchParams.parse_integer("point_id")
  const form_id = searchParams.parse_integer("form_id")
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(project_id, material_id, point_id, form_id, offset, limit)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewMaterialAllocat = await request.json()
    const item = await insert(data)
    return Response.json(item)
  } else if (request.method === Method.PUT) {
    const data: UpdateMaterialAllocat = await request.json()

    const item = await update(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
