import { type LoaderFunctionArgs } from "@remix-run/node"

import { point_type_z } from "~/schema/point_types"
import { point_status_z } from "~/schema/points"
import { get_count } from "~/models/points.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id", true) as number
    const point_type = searchParams.parse_zod("point_type", point_type_z)
    const status = searchParams.parse_zod("status", point_status_z)

    const result = await get_count(project_id, point_type, status)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)
