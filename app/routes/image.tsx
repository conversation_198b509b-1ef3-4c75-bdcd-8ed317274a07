import type { ActionFunctionArgs } from "@remix-run/node"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    if (request.method === Method.POST) {
      const formData = await request.formData()
      const image_file = formData.get("image") as File
      if (!image_file) throw new Response("image missing", { status: Status.BadRequest })

      const project_id = formData.get("project_id")
      if (!project_id) throw new Response("project_id missing", { status: Status.BadRequest })
      const submission_id = formData.get("submiting_id")
      if (!submission_id) throw new Response("submiting_id missing", { status: Status.BadRequest })
      const unit_slug = formData.get("unit_slug")
      if (!unit_slug) throw new Response("unit_slug missing", { status: Status.BadRequest })

      const key = `${project_id}/${submission_id}/${unit_slug}_${image_file.name}`

      await storage_put("images", image_file, key)
      return new Response(`${unit_slug}_${image_file.name}`, { status: Status.Ok })
    }

    return new Response("not allowed", { status: Status.MethodNotAllowed })
  },
  { auth: false },
)

export default function Component() {
  return (
    <>
      <form method="post" encType="multipart/form-data">
        <input type="number" name="project_id" />
        <input type="number" name="submiting_id" />
        <input type="text" name="unit_slug" />
        <input type="file" name="image" accept="image/*" />
        <button>Submit</button>
      </form>
    </>
  )
}
