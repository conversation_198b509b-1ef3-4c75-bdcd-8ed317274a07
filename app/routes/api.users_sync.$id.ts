import type { ActionFunctionArgs } from "@remix-run/node"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { sync } from "~/models/users.server"
import { role_z, type User } from "~/schema/users"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request, context, params }: ActionFunctionArgs) => {
  const user = context.user as User
  if (user.role !== role_z.enum.admin) {
    throw new Response("not allowed", { status: Status.Forbidden })
  }

  if (request.method === Method.PUT) {
    invariant(params.id, "missing id")
    const user_id = parseInt(params.id, 10)

    const item = await sync(user_id)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
