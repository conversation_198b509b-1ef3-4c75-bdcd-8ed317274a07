import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/materials.server"
import { type NewMaterial } from "~/schema/materials"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const project_id = searchParams.parse_integer("project_id", true) as number
  const code = searchParams.get("code") ?? undefined
  const name = searchParams.get("name") ?? undefined
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")
  const image_stat = searchParams.parse_boolean("image_stat")

  const result = await find(project_id, code, name, offset, limit, image_stat)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewMaterial = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
