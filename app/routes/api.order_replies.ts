import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/order_replies.server"
import { type NewOrderReply } from "~/schema/order_replies"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const order_id = searchParams.parse_integer("order_id")
  const content = searchParams.get("content") ?? undefined
  const creator_id = searchParams.parse_integer("creator_id")
  const offset = searchParams.parse_integer("offset")
  const limit = searchParams.parse_integer("limit")

  const result = await find(order_id, content, creator_id, offset, limit)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewOrderReply = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
