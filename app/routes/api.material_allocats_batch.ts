import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { insert_batch, remove_batch, update_batch } from "~/models/material_allocats.server"
import type { MaterialAllocat, NewMaterialAllocat, UpdateMaterialAllocat } from "~/schema/material_allocats"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewMaterialAllocat[] = await request.json()
    const item = await insert_batch(data)
    return Response.json(item)
  } else if (request.method === Method.PUT) {
    const data: UpdateMaterialAllocat[] = await request.json()
    const items = await update_batch(data)
    return Response.json(items)
  } else if (request.method === Method.DELETE) {
    const data: MaterialAllocat[] = await request.json()
    await remove_batch(data)
    return new Response("deleted", { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
