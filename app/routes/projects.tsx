import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"
import { useLoaderData, redirect } from "@remix-run/react"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { auth } from "~/auth.form.server"
import { find, insert } from "~/models/projects.server"
import { type NewProject } from "~/schema/projects"

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await auth.isAuthenticated(request, { failureRedirect: "/signin" })

  const { searchParams } = new URL(request.url)
  const name = searchParams.get("name") ?? undefined

  const result = await find(user.id, name)
  return Response.json(result)
}

export const action = async ({ request }: ActionFunctionArgs) => {
  await auth.isAuthenticated(request, { failureRedirect: "/signin" })

  if (request.method === Method.POST) {
    const data: NewProject = await request.json()
    const project = await insert(data)
    return redirect("/projects/" + project.id)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
}

export default function Projects() {
  const data = useLoaderData<typeof loader>()
  return JSON.stringify(data)
}
