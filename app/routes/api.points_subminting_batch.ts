import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { type NewPointSubmissionForBatch, insert_batch_with_submission } from "~/models/points.batch.server"
import Handle from "~/utils/handle_request"
import { User } from "~/schema/users"
import { cleanCachified } from "~/utils/cache"

export const action = Handle(async ({ request, context }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data_batch: NewPointSubmissionForBatch[] = await request.json()

    const user = context.user as User
    for (const data of data_batch) {
      data.submiting.user_id = user.id
    }

    const project = await insert_batch_with_submission(data_batch)
    cleanCachified("points", "submission")

    return Response.json(project)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
