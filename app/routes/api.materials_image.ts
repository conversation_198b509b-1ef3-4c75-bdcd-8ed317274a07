import type { ActionFunctionArgs } from "@remix-run/node"
import path from "path"

import { storage_put } from "~/storage.server"
import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const formData = await request.formData()
    const image_file = formData.get("image") as File
    if (!image_file) throw new Response("image missing", { status: Status.BadRequest })

    const project_id = formData.get("project_id")
    if (!project_id) throw new Response("project_id missing", { status: Status.BadRequest })
    const material_id = formData.get("material_id")
    if (!material_id) throw new Response("material_id missing", { status: Status.BadRequest })
    const facet = formData.get("facet")
    if (!facet) throw new Response("facet missing", { status: Status.BadRequest })

    const key = `${project_id}/${material_id}/${facet}${path.extname(image_file.name)}`

    await storage_put("material-images", image_file, key)
    return new Response(key, { status: Status.Ok })
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
