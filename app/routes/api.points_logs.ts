import { type LoaderFunctionArgs } from "@remix-run/node"

import { getLogs } from "~/models/points.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const point_id = searchParams.parse_integer("point_id", true) as number

    const result = await getLogs(point_id)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)
