import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/projects.server"
import { type NewProject } from "~/schema/projects"
import Handle from "~/utils/handle_request"
import { User } from "~/schema/users"

export const loader = Handle(async ({ request, context }: LoaderFunctionArgs) => {
  const { searchParams } = new URL(request.url)
  const name = searchParams.get("name") ?? undefined

  const user = context.user as User
  const result = await find(user.id, name)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewProject = await request.json()
    const project = await insert(data)
    return Response.json(project)
  }

  return new Response("Not Allowed", { status: Status.MethodNotAllowed })
})
