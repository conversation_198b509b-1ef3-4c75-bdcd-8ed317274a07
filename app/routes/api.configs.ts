import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import { find, insert } from "~/models/configs.server"
import { type NewConfig } from "~/schema/configs"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)
  const cate_id = searchParams.parse_integer("cate_id")
  const code = searchParams.get("code") ?? undefined
  const name = searchParams.get("name") ?? undefined

  const result = await find(cate_id, code, name)
  return Response.json(result)
})

export const action = Handle(async ({ request }: ActionFunctionArgs) => {
  if (request.method === Method.POST) {
    const data: NewConfig = await request.json()
    const item = await insert(data)
    return Response.json(item)
  }

  return new Response("not allowed", { status: Status.MethodNotAllowed })
})
