import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"
import { composeImages } from "~/utils/image_compose"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    if (request.method !== Method.POST) {
      return new Response("Method not allowed", { status: Status.MethodNotAllowed })
    }

    try {
      const formData = await request.formData()

      // Extract parameters
      const text = formData.get("text") as string | null
      const imageUrlA = formData.get("image_url_a") as string | null
      const imageUrlB = formData.get("image_url_b") as string | null

      // Validate required parameters
      if (!text) {
        return new Response("Missing required parameter: text", { status: Status.BadRequest })
      }
      if (!imageUrlA) {
        return new Response("Missing required parameter: image_url_a", { status: Status.BadRequest })
      }
      if (!imageUrlB) {
        return new Response("Missing required parameter: image_url_b", { status: Status.BadRequest })
      }

      // Validate URLs
      try {
        new URL(imageUrlA)
        new URL(imageUrlB)
      } catch (error) {
        return new Response("Invalid image URL format", { status: Status.BadRequest })
      }

      // Compose the images
      const result = await composeImages(text, imageUrlA, imageUrlB)

      // Return the composed image
      return new Response(result.buffer, {
        status: Status.Ok,
        headers: {
          "Content-Type": "image/png",
          "Content-Length": result.buffer.length.toString(),
          "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        },
      })
    } catch (error) {
      console.error("Image composition error:", error)

      // Handle specific error types
      if (error instanceof Error) {
        if (error.message.includes("Failed to fetch image")) {
          return new Response(`Image fetch error: ${error.message}`, { status: Status.BadRequest })
        }
        if (error.message.includes("Invalid image metadata")) {
          return new Response(`Invalid image: ${error.message}`, { status: Status.BadRequest })
        }
      }

      return new Response("Internal server error during image composition", {
        status: Status.InternalServerError,
      })
    }
  },
  { auth: false },
)
