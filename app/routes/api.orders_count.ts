import type { LoaderFunctionArgs } from "@remix-run/node"

import { get_count } from "~/models/orders.server"
import { order_status_z } from "~/schema/orders"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"

export const loader = Handle(async ({ request, context }: LoaderFunctionArgs) => {
  const searchParams = new URLSearchParamsExtended(request.url)

  const project_id = searchParams.parse_integer("project_id")
  const point_id = searchParams.parse_integer("point_id")
  const creator_id = searchParams.parse_integer("creator_id")
  const handler_id = searchParams.parse_integer("handler_id")
  const order_status = searchParams.parse_zod("order_status", order_status_z)

  const result = await get_count(project_id, point_id, creator_id, handler_id, order_status)
  return Response.json(result)
})
