import type { LoaderFunctionArgs } from "@remix-run/node"

import { find_prepare, find } from "~/models/points.query.server"
import { URLSearchParamsExtended } from "~/utils/params_parse"
import Handle from "~/utils/handle_request"
import { point_type_z } from "~/schema/point_types"

export const loader = Handle(
  async ({ request }: LoaderFunctionArgs) => {
    const searchParams = new URLSearchParamsExtended(request.url)
    const project_id = searchParams.parse_integer("project_id", true) as number
    const point_type = searchParams.parse_zod("point_type", point_type_z)
    const point_id = searchParams.parse_integer("point_id")
    const offset = searchParams.parse_integer("offset")
    const limit = searchParams.parse_integer("limit")
    // const orders = searchParams.parse_orders()

    const result = await find_prepare(project_id, point_type, point_id, offset, limit)
    return Response.json(result)
  },
  { auth: true, cachified: "points" },
)
