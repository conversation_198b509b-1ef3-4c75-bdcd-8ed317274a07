import { Client as MinioClient, CopySourceOptions, CopyDestinationOptions } from "minio"
import { HttpStatusCode as Status } from "~/utils/http_code"

const mclient = new MinioClient({
  endPoint: process.env.MINIO_ENDPOINT!,
  port: parseInt(process.env.MINIO_PORT!),
  useSSL: false,
  accessKey: process.env.MINIO_ACCESSKEY!,
  secretKey: process.env.MINIO_SECRETKEY!,
})

export async function stat(bucket: string, key: string): Promise<boolean> {
  try {
    const stat = await mclient.statObject(bucket, key)
    return true
  } catch (error) {
    console.error("stat failed", error)
    return false
  }
}

export async function storage_put(bucket: string, file: File, key: string): Promise<void> {
  try {
    // https://aneacsu.com/blog/2024-04-19-minio-with-sveltekit
    const fileStream = Buffer.from(await file.arrayBuffer())
    await mclient.putObject(bucket, key, fileStream, fileStream.byteLength, {
      "Content-Type": file.type,
    })
    console.info("storage done", key)
  } catch (error) {
    console.error("storage failed", error)
    throw new Response("storage failed", { status: Status.InternalServerError })
  }
}

export async function storage_copy(
  bucket: string,
  source_key: string,
  dest_key: string,
  remove_source = true,
): Promise<void> {
  try {
    await mclient.copyObject(
      new CopySourceOptions({ Bucket: bucket, Object: source_key }),
      new CopyDestinationOptions({ Bucket: bucket, Object: dest_key }),
    )
    if (remove_source) await mclient.removeObject(bucket, source_key)
  } catch (error) {
    console.error("storage_move failed", error)
    throw new Response("storage_move failed", { status: Status.InternalServerError })
  }
}

// https://reactrouter.com/how-to/file-uploads
// import { LocalFileStorage } from "@mjackson/file-storage/local"

// export const imagesStorage = new LocalFileStorage("./uploads/images")

// export function getImageStorageKey(submission_id: number, name: string) {
//   return `images-${submission_id}-${name}`
// }
