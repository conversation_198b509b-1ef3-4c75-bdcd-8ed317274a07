import { relations } from "drizzle-orm"
import {
  integer as int,
  pgTable as table,
  index,
  varchar,
  AnyPgColumn,
  pgEnum,
  timestamp,
} from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { z } from "zod"
// import { generateUnique } from "~/utils/generate_unique"

import { projects } from "./projects"
import { submissions } from "./submissions"
import { point_types } from "./point_types"

export const point_status = pgEnum("form_status", ["normal", "temporary", "issue", "frozen"])
export const point_status_z = createSelectSchema(point_status)
export type PointStatus = z.infer<typeof point_status_z>

export const points = table(
  "points",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    name: varchar({ length: 256 }).notNull(),
    slug: varchar({ length: 256 }).notNull(),
    status: point_status().notNull().default("temporary"),
    create_time: timestamp({ mode: "string" }).defaultNow(),
    update_time: timestamp({ mode: "string" }).defaultNow(),
    // .$default(() => generateUnique(16)),
    parent_id: int().references((): AnyPgColumn => points.id, {
      onDelete: "set null",
    }),
    project_id: int()
      .references(() => projects.id, {
        onDelete: "cascade",
      })
      .notNull(),
    type: point_types().notNull(),
  },
  (t) => [
    index("points_slug_idx").on(t.project_id, t.slug),
    index("points_type_idx").on(t.project_id, t.type),
    index("points_status_idx").on(t.project_id, t.status),
  ],
)

export const point_relations = relations(points, ({ many, one }) => ({
  submissions: many(submissions),
  parent: one(points, {
    fields: [points.parent_id],
    references: [points.id],
  }),
  project: one(projects, {
    fields: [points.project_id],
    references: [projects.id],
  }),
}))

export type Point = typeof points.$inferSelect
export type NewPoint = typeof points.$inferInsert
export type UpdatePoint = Partial<Point> & { id: number }

export const point_zs = createSelectSchema(points)
export const point_zi = createInsertSchema(points)
export const point_zu = createUpdateSchema(points)
