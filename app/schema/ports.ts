import { relations } from "drizzle-orm"
import { integer as int, pgEnum, pgTable as table, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"
import { points } from "./points"
import { cabinets } from "./cabinets"
import { materials } from "./materials"
import { z } from "zod"

export const port_setup_direction = pgEnum("port_setup_direction", [
  "front_horizontal",
  "front_vertical",
  "opposite_horizontal",
  "opposite_vertical",
])
export const port_setup_directionz = createSelectSchema(port_setup_direction)
export type PortSetupDirection = z.infer<typeof port_setup_directionz>

export const port_usage = pgEnum("port_usage", ["communicate", "power", "control", "security"])
export const port_usagez = createSelectSchema(port_usage)
export type PortUsage = z.infer<typeof port_usagez>

export const ports = table("ports", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  project_id: int()
    .references(() => projects.id, {
      onDelete: "cascade",
    })
    .notNull(),
  material_id: int()
    .references(() => materials.id, {
      onDelete: "cascade",
    })
    .notNull(),
  setup_direction: port_setup_direction(),
  port_usage: port_usage(),
  card_model: varchar({ length: 128 }),
  slot_no: varchar({ length: 64 }).notNull(),
  port_no: varchar({ length: 64 }).notNull(),
  port_name: varchar({ length: 64 }),
  port_standard: varchar({ length: 64 }),
  interface_type: varchar({ length: 64 }),
  index: int(),
})

export const ports_relations = relations(ports, ({ one }) => ({
  project: one(projects, {
    fields: [ports.project_id],
    references: [projects.id],
  }),
  material: one(materials, {
    fields: [ports.material_id],
    references: [materials.id],
  }),
}))

export type Port = typeof ports.$inferSelect
export type NewPort = typeof ports.$inferInsert
export type UpdatePort = Partial<Port> & { id: number }

export const ports_zs = createSelectSchema(ports)
export const ports_zi = createInsertSchema(ports)
export const ports_zu = createUpdateSchema(ports)
