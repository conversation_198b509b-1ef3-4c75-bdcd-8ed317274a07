import { z } from "zod"

import { type Project } from "./projects"
import { type NewUnit, type Unit, unit_zi, unit_zu, UpdateUnit } from "./units"
import { type Submission } from "./submissions"
import { type Form, type NewForm, type UpdateForm, form_zi, form_zu } from "./forms"

export type FormCombine = Form & {
  units: Unit[]
  submitings?: Submission[]
  project?: Project
}

export type NewFormCombine = NewForm & {
  units: NewUnit[]
}

export type UpdateFormCombine = UpdateForm & {
  units: (NewUnit | UpdateUnit)[]
}

const unit_combine_zi = unit_zi.extend({
  form_id: z.undefined(),
})

export const form_combine_zi = form_zi.extend({
  units: z.array(unit_combine_zi),
})

export const form_combine_zu = form_zu.extend({
  units: z.array(z.union([unit_combine_zi, unit_zu])),
})
