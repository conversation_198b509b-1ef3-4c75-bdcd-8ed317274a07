// import { z } from "zod"

// import { value_zi, value_zu, type Value, type NewValue, type UpdateValue } from "./values"
// import { type User } from "./users"
// import { type Form } from "./forms"
// import { type Point } from "./points"
// import {
//   type NewSubmiting,
//   type Submiting,
//   type UpdateSubmiting,
//   submiting_zi,
//   submiting_zu,
// } from "./submitings"

// export type SubmitingCombine = Submiting & {
//   values: Value[]
//   point?: Point
//   form?: Form
//   user?: User
// }

// export type NewSubmitingCombine = NewSubmiting & {
//   values: NewValue[]
// }

// export type UpdateSubmitingCombine = UpdateSubmiting & {
//   values: (UpdateValue | NewValue)[]
// }

// const value_combine_zi = value_zi.extend({
//   submiting_id: z.undefined(),
// })

// export const submiting_combine_zi = submiting_zi.extend({
//   values: z.array(value_combine_zi),
// })

// export const submiting_combine_zu = submiting_zu.extend({
//   values: z.array(z.union([value_combine_zi, value_zu])),
// })
