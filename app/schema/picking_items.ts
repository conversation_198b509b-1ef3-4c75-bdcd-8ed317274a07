import { relations, sql } from "drizzle-orm"
import { integer as int, pgTable as table, varchar } from "drizzle-orm/pg-core"
import { pickings } from "./pickings"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { materials } from "./materials"

export const picking_items = table("picking_items", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  picking_id: varchar({ length: 64 })
    .notNull()
    .references(() => pickings.id, {
      onDelete: "cascade",
    }),
  material_id: int()
    .notNull()
    .references(() => materials.id, {
      onDelete: "restrict",
    }),
  count: int(),
  serial_numbers: varchar({ length: 64 })
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`),
})

export const picking_item_relations = relations(picking_items, ({ one }) => ({
  picking: one(pickings, {
    fields: [picking_items.picking_id],
    references: [pickings.id],
  }),
  material: one(materials, {
    fields: [picking_items.material_id],
    references: [materials.id],
  }),
}))

export type PickingItem = typeof picking_items.$inferSelect
export type NewPickingItem = typeof picking_items.$inferInsert
export type UpdatePickingItem = Partial<PickingItem> & { id: number }

export const picking_item_zi = createInsertSchema(picking_items)
export const picking_item_zu = createUpdateSchema(picking_items)
