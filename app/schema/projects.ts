import { relations, sql } from "drizzle-orm"
import { integer as int, pgTable as table, timestamp, index, varchar, point } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { generateUnique } from "~/utils/generate_unique"
import { users } from "./users"
import { forms } from "./forms"
import { points } from "./points"

export const projects = table(
  "projects",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    name: varchar({ length: 256 }).notNull(),
    slug: varchar({ length: 256 })
      .unique()
      .$default(() => generateUnique(16)),
    create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
    manager_id: int().references(() => users.id, {
      onDelete: "set null",
    }),
    order_manager_id: int().references(() => users.id, {
      onDelete: "set null",
    }),
    order_handler_id: int().references(() => users.id, {
      onDelete: "set null",
    }),
    users: int()
      .array()
      .notNull()
      .default(sql`ARRAY[]::integer[]`), // https://orm.drizzle.team/docs/guides/empty-array-default-value#postgresql
    oa_project_id: varchar({ length: 64 }),
    location: point({ mode: "xy" }),
  },
  (t) => [index("projects_slug_idx").on(t.slug)],
)

export const project_relations = relations(projects, ({ many, one }) => ({
  forms: many(forms),
  points: many(points),
  manager: one(users, {
    fields: [projects.manager_id],
    references: [users.id],
  }),
  order_manager: one(users, {
    fields: [projects.order_manager_id],
    references: [users.id],
  }),
  order_handler: one(users, {
    fields: [projects.order_handler_id],
    references: [users.id],
  }),
}))

export type Project = typeof projects.$inferSelect
export type NewProject = typeof projects.$inferInsert
export type UpdateProject = Partial<Project> & { readonly id: number }

export const project_zs = createSelectSchema(projects)
export const project_zi = createInsertSchema(projects)
export const project_zu = createUpdateSchema(projects)
