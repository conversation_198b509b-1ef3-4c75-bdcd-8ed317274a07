import { integer as int, primaryKey, real, pgTable as table } from "drizzle-orm/pg-core"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"
import { relations } from "drizzle-orm"
import { points } from "./points"
import { forms } from "./forms"
import { materials } from "./materials"

export const material_allocats = table(
  "material_allocats",
  {
    // id: int().primaryKey().generatedByDefaultAsIdentity(),
    material_id: int()
      .references(() => materials.id, {
        onDelete: "cascade",
      })
      .notNull(),
    project_id: int()
      .references(() => projects.id, {
        onDelete: "cascade",
      })
      .notNull(),
    point_id: int()
      .references(() => points.id, {
        onDelete: "cascade",
      })
      .notNull(),
    form_id: int()
      .references(() => forms.id, {
        onDelete: "cascade",
      })
      .notNull(),
    total_num: real(),
    complete_num: real(),
  },
  (t) => [primaryKey({ columns: [t.project_id, t.material_id, t.point_id, t.form_id] })],
)

export const material_allocat_relations = relations(material_allocats, ({ one }) => ({
  material: one(materials, {
    fields: [material_allocats.material_id],
    references: [materials.id],
  }),
  project: one(projects, {
    fields: [material_allocats.project_id],
    references: [projects.id],
  }),
  point: one(points, {
    fields: [material_allocats.point_id],
    references: [points.id],
  }),
  form: one(forms, {
    fields: [material_allocats.form_id],
    references: [forms.id],
  }),
}))

export type MaterialAllocat = typeof material_allocats.$inferSelect
export type NewMaterialAllocat = typeof material_allocats.$inferInsert
export type UpdateMaterialAllocat = Partial<MaterialAllocat> & {
  project_id: number
  material_id: number
  point_id: number
  form_id: number
}

export const material_allocat_zi = createInsertSchema(material_allocats)
export const material_allocat_zu = createUpdateSchema(material_allocats)
