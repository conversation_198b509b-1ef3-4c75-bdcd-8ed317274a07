import { integer as int, pgTable as table, timestamp, varchar, jsonb, boolean } from "drizzle-orm/pg-core"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"

export const submission_logs = table("submiting_logs", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  submiting_id: int().notNull(),
  submitter_name: varchar({ length: 256 }).notNull(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  project_id: int().notNull(),
  point_id: int().notNull(),
  form_id: int().notNull(),
  user_id: int().notNull(),
  material_id: int(),
  values: jsonb(),
  done: boolean(),
})

export type SubmissionLog = typeof submission_logs.$inferSelect
export type NewSubmissionLog = typeof submission_logs.$inferInsert
export type UpdateSubmissionLog = Partial<SubmissionLog> & { id: number }

export const submission_logs_zi = createInsertSchema(submission_logs)
export const submission_logs_zu = createUpdateSchema(submission_logs)
