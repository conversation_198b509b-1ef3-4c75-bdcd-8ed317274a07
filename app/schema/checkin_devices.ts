import { pgTable as table, integer as int, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"

export const checkin_devices = table("checkin_devices", {
  id: varchar({ length: 64 }).primaryKey(),
  name: varchar({ length: 64 }),
  project_id: int().references(() => projects.id, {
    onDelete: "set null",
  }),
})

export type CheckInDevice = typeof checkin_devices.$inferSelect
export type NewCheckInDevice = typeof checkin_devices.$inferInsert
export type UpdateCheckInDevice = Partial<CheckInDevice> & { serial_number: string }

export const checkin_device_zs = createSelectSchema(checkin_devices)
export const checkin_device_zi = createInsertSchema(checkin_devices)
export const checkin_device_zu = createUpdateSchema(checkin_devices)
