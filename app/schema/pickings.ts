import { relations, sql } from "drizzle-orm"
import {
  integer as int,
  pgTable as table,
  varchar,
  AnyPgColumn,
  timestamp,
  boolean,
  pgEnum,
} from "drizzle-orm/pg-core"
import { z } from "zod"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { users } from "./users"
import { projects } from "./projects"
import { picking_items, type PickingItem, type UpdatePickingItem, type NewPickingItem } from "./picking_items"

export const document_types = pgEnum("document_types", ["picking", "arrived"])
export const document_type_z = createSelectSchema(document_types)
export type DocumentType = z.infer<typeof document_type_z>

export const pickings = table("pickings", {
  id: varchar({ length: 64 }).primaryKey(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  time: timestamp({ mode: "string" }).notNull().defaultNow(),
  type: document_types().default("picking"),
  project_id: int()
    .notNull()
    .references((): AnyPgColumn => projects.id, {
      onDelete: "cascade",
    }),
  creator_id: int()
    .notNull()
    .references(() => users.id, {
      onDelete: "restrict",
    }),
  receiver_id: int()
    .notNull()
    .references(() => users.id, {
      onDelete: "restrict",
    }),
  attachments: varchar({ length: 64 })
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`),
  formal: boolean().default(false),
  authority_unit: varchar({ length: 64 }),
  signatory_unit: varchar({ length: 64 }),
})

export const issues_relations = relations(pickings, ({ one, many }) => ({
  project: one(projects, {
    fields: [pickings.project_id],
    references: [projects.id],
  }),
  creator: one(users, {
    fields: [pickings.creator_id],
    references: [users.id],
  }),
  receiver: one(users, {
    fields: [pickings.receiver_id],
    references: [users.id],
  }),
  items: many(picking_items),
}))

export type Picking = typeof pickings.$inferSelect
export type UpdatePicking = Partial<Picking> & { id: string }

export type PickingWith = Picking & {
  items: PickingItem[]
}
export type UpdatePickingWith = UpdatePicking & {
  items: Array<UpdatePickingItem | NewPickingItem>
}

export const picking_zs = createSelectSchema(pickings)
export const picking_zi = createInsertSchema(pickings)
export const picking_zu = createUpdateSchema(pickings)
