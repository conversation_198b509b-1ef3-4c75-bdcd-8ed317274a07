import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"
import { points } from "./points"
import { materials } from "./materials"
import { cabinet_materials } from "./cabinet_materials"

export const cabinets = table("cabinets", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  project_id: int()
    .references(() => projects.id, {
      onDelete: "cascade",
    })
    .notNull(),
  point_id: int()
    .references(() => points.id, {
      onDelete: "cascade",
    })
    .notNull(),
  material_id: int()
    .references(() => materials.id, {
      onDelete: "cascade",
    })
    .notNull(),
  room_code: varchar({ length: 64 }).notNull(),
  code: varchar({ length: 64 }).notNull(),
  type: varchar({ length: 64 }),
  index: int().notNull(),
})

export const cabinet_relations = relations(cabinets, ({ one, many }) => ({
  project: one(projects, {
    fields: [cabinets.project_id],
    references: [projects.id],
  }),
  point: one(points, {
    fields: [cabinets.point_id],
    references: [points.id],
  }),
  material_type: one(materials, {
    fields: [cabinets.material_id],
    references: [materials.id],
  }),
  materials: many(cabinet_materials),
}))

export type Cabinet = typeof cabinets.$inferSelect
export type NewCabinet = typeof cabinets.$inferInsert
export type UpdateCabinet = Partial<Cabinet> & { id: number }

export const cabinet_zs = createSelectSchema(cabinets)
export const cabinet_zi = createInsertSchema(cabinets)
export const cabinet_zu = createUpdateSchema(cabinets)
