import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, index, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"

export const config_cates = table(
  "config_cates",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    project_id: int().references(() => projects.id, {
      onDelete: "set null",
    }),
    slug: varchar({ length: 64 }).notNull(),
    name: varchar({ length: 256 }).notNull(),
    description: varchar({ length: 1024 }),
  },
  (t) => [index("config_cates_slug_idx").on(t.slug)],
)

export const config_cates_relations = relations(config_cates, ({ one }) => ({
  project: one(projects, {
    fields: [config_cates.project_id],
    references: [projects.id],
  }),
}))

export type ConfigCate = typeof config_cates.$inferSelect
export type NewConfigCate = typeof config_cates.$inferInsert
export type UpdateConfigCate = Partial<ConfigCate> & { id: number }

export const config_cate_zs = createSelectSchema(config_cates)
export const config_cate_zi = createInsertSchema(config_cates)
export const config_cate_zu = createUpdateSchema(config_cates)
