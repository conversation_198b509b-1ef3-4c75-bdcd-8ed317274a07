import { relations } from "drizzle-orm"
import { z } from "zod"
import { integer as int, pgTable as table, index, varchar, boolean, pgEnum } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema, createUpdateSchema } from "drizzle-zod"

import { generateUnique } from "~/utils/generate_unique"
import { projects } from "./projects"
import { units } from "./units"
import { submissions } from "./submissions"

export const form_types = pgEnum("form_types", ["lane_setup", "device_setup", "other"])
export const form_type_z = createSelectSchema(form_types)
export type FormType = z.infer<typeof form_type_z>

export const forms = table(
  "forms",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    name: varchar({ length: 256 }).notNull(),
    slug: varchar({ length: 256 })
      .notNull()
      .$default(() => generateUnique(16)),
    type: form_types().default("other"),
    index: int(),
    icon: varchar({ length: 64 }),
    singleton_submiting: boolean().default(true),
    project_id: int()
      .references(() => projects.id, {
        onDelete: "cascade",
      })
      .notNull(),
    description: varchar({ length: 1024 }),
  },
  (t) => [index("forms_slug_idx").on(t.slug)],
)

export const form_relations = relations(forms, ({ many, one }) => ({
  units: many(units),
  submissions: many(submissions),
  project: one(projects, {
    fields: [forms.project_id],
    references: [projects.id],
  }),
}))

export type Form = typeof forms.$inferSelect
export type NewForm = typeof forms.$inferInsert
export type UpdateForm = Partial<Form> & { id: number }

export const form_zi = createInsertSchema(forms)
export const form_zu = createUpdateSchema(forms)
