import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, varchar, timestamp } from "drizzle-orm/pg-core"
import { issues } from "./issues"
import { users } from "./users"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"

export const issue_replies = table("issue_replys", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  issue_id: int()
    .notNull()
    .references(() => issues.id, {
      onDelete: "cascade",
    }),
  creator_id: int().references(() => users.id, {
    onDelete: "set null",
  }),
  content: varchar({ length: 1024 }),
  responsible_party: varchar({ length: 256 }),
  solution: varchar({ length: 1024 }),
  solution_point_ids: int().array(),
})

export const issue_reply_relations = relations(issue_replies, ({ one }) => ({
  issus: one(issues, {
    fields: [issue_replies.issue_id],
    references: [issues.id],
  }),
  creator: one(users, {
    fields: [issue_replies.creator_id],
    references: [users.id],
  }),
}))

export type IssueReply = typeof issue_replies.$inferSelect
export type NewIssueReply = typeof issue_replies.$inferInsert
export type UpdateIssueReply = Partial<IssueReply> & { id: number }

export const issue_reply_zi = createInsertSchema(issue_replies)
export const issue_reply_zu = createUpdateSchema(issue_replies)
