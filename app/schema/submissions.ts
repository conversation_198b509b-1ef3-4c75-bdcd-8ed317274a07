import { relations, sql } from "drizzle-orm"
import {
  integer as int,
  pgTable as table,
  timestamp,
  varchar,
  real,
  jsonb,
  boolean,
  pgEnum,
  index,
} from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { z } from "zod"

// import { values } from "./values"
import { users } from "./users"
import { forms } from "./forms"
import { points } from "./points"
import { projects } from "./projects"
import { materials } from "./materials"
import { cabinet_materials } from "./cabinet_materials"

export const submission_status = pgEnum("submitting_status", [
  "unfinished",
  "wait_rectified",
  "rectified",
  "finished",
])
export const submission_status_z = createSelectSchema(submission_status)
export type SubmissionStatus = z.infer<typeof submission_status_z>

export const submissions = table(
  "submiting",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    project_id: int()
      .notNull()
      .references(() => projects.id, {
        onDelete: "cascade",
      }),
    create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
    update_time: timestamp({ mode: "string" }).notNull().defaultNow(),
    point_id: int()
      .notNull()
      .references(() => points.id, {
        onDelete: "cascade",
      }),
    form_id: int()
      .notNull()
      .references(() => forms.id, {
        onDelete: "cascade",
      }),
    user_id: int()
      .notNull()
      .references(() => users.id, {
        onDelete: "set null",
      }),
    material_id: int().references(() => materials.id, {
      onDelete: "cascade",
    }),
    cabinet_material_id: int().references(() => cabinet_materials.id, {
      onDelete: "cascade",
    }),
    values: jsonb(),
    orgs: varchar().array(),
    status: submission_status().notNull().default("unfinished"),
    done: boolean().default(false),
    done_user: int().references(() => users.id, {
      onDelete: "set null",
    }),
    user_ids: int().array(),
    plan_time: timestamp({ mode: "string" }),
  },
  (t) => [
    index("submissions_form_idx").on(t.project_id, t.form_id),
    index("submissions_point_form_idx").on(t.project_id, t.point_id, t.form_id),
    index("submissions_user_done_idx").on(t.project_id, t.user_id, t.done),
    index("submissions_material_idx").on(t.project_id, t.material_id),
    index("submissions_cabinet_material_idx").on(t.project_id, t.cabinet_material_id),
    index("submissions_status_idx").on(t.project_id, t.status),
    // index("submissions_create_time_idx").on(t.project_id, t.create_time),
    // index("submissions_plan_time_idx").on(t.project_id, t.plan_time),
  ],
)

export const submission_relations = relations(submissions, ({ many, one }) => ({
  // values: many(values),
  project: one(projects, {
    fields: [submissions.project_id],
    references: [projects.id],
  }),
  point: one(points, {
    fields: [submissions.point_id],
    references: [points.id],
  }),
  form: one(forms, {
    fields: [submissions.form_id],
    references: [forms.id],
  }),
  user: one(users, {
    fields: [submissions.user_id],
    references: [users.id],
  }),
  material: one(materials, {
    fields: [submissions.material_id],
    references: [materials.id],
  }),
  cabinet_material: one(cabinet_materials, {
    fields: [submissions.cabinet_material_id],
    references: [cabinet_materials.id],
  }),
}))

export type Submission = typeof submissions.$inferSelect
export type NewSubmission = typeof submissions.$inferInsert
export type UpdateSubmission = Partial<Submission> & { id: number }

export const submission_zi = createInsertSchema(submissions)
export const submission_zu = createUpdateSchema(submissions)
