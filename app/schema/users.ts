import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, boolean, timestamp, pgEnum, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { z } from "zod"

import { submissions } from "./submissions"
import { projects } from "./projects"
import { accounts } from "./accounts"

export const roles = pgEnum("roles", ["user", "manager", "admin"])
export const role_z = createSelectSchema(roles)
export type Role = z.infer<typeof role_z>

export const genders = pgEnum("genders", ["male", "female"])
export const gender_z = createSelectSchema(genders)
export type Gender = z.infer<typeof gender_z>

export const users = table("users", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  name_chinese: varchar({ length: 64 }),
  account_id: int().references(() => accounts.id, {
    onDelete: "set null",
  }),
  role: roles().default("user"),
  phone: varchar({ length: 64 }), // .unique(),
  org: varchar({ length: 64 }),
  disable: boolean().default(false),
  identity: varchar({ length: 64 }),
  bankcard: varchar({ length: 64 }),
  gender: genders(),
  attendance: boolean().default(false),
  using: boolean().default(false),
})

export const user_relations = relations(users, ({ many, one }) => ({
  submissions: many(submissions),
  projects: many(projects),
  account: one(accounts, {
    fields: [users.account_id],
    references: [accounts.id],
  }),
}))

export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert
export type AddUser = typeof users.$inferInsert & { account_id: number }
export type UpdateUser = Partial<User> & { id: number }

export const user_zs = createSelectSchema(users)
export const user_zi = createInsertSchema(users)
export const user_za = createInsertSchema(users).required({
  account_id: true,
})
export const user_zu = createUpdateSchema(users)
