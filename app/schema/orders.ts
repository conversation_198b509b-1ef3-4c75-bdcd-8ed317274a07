import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, varchar, pgEnum, timestamp } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { z } from "zod"

import { projects } from "./projects"
import { points } from "./points"
import { users } from "./users"

export const order_status = pgEnum("order_status", ["unprocessed", "processed", "closed"])
export const order_status_z = createSelectSchema(order_status)
export type OrderStatus = z.infer<typeof order_status_z>

export const orders = table("orders", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  update_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  status: order_status().notNull().default("unprocessed"),
  project_id: int()
    .references(() => projects.id, {
      onDelete: "cascade",
    })
    .notNull(),
  point_id: int()
    .references(() => points.id, {
      onDelete: "cascade",
    })
    .notNull(),
  title: varchar({ length: 256 }).notNull(),
  description: varchar({ length: 1024 }),
  creator_id: int().references(() => users.id, {
    onDelete: "set null",
  }),
  handler_id: int().references(() => users.id, {
    onDelete: "set null",
  }),
})

export const orders_relations = relations(orders, ({ one }) => ({
  project: one(projects, {
    fields: [orders.project_id],
    references: [projects.id],
  }),
  point: one(points, {
    fields: [orders.point_id],
    references: [points.id],
  }),
  creator: one(users, {
    fields: [orders.creator_id],
    references: [users.id],
  }),
  handler: one(users, {
    fields: [orders.handler_id],
    references: [users.id],
  }),
}))

export type Order = typeof orders.$inferSelect
export type NewOrder = typeof orders.$inferInsert
export type UpdateOrder = Partial<Order> & { id: number }

export const order_zs = createSelectSchema(orders)
export const order_zi = createInsertSchema(orders)
export const order_zu = createUpdateSchema(orders)
