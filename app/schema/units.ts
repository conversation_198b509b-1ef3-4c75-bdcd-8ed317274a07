import { relations, sql } from "drizzle-orm"
import { integer as int, pgTable as table, boolean, index, json, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { forms } from "./forms"
// import { values } from "./values"

// export const unit_types = pgEnum("unit_types", [
//   "text",
//   "number",
//   "range",
//   "date",
//   "time",
//   "datetime",
//   "phone",
//   "email",
//   "url",
//   "select",
//   "select-multiple",
//   "file",
//   "file-multiple",
//   "dividing",
// ])
// export const unit_type_z = createSelectSchema(unit_types)

export const units = table(
  "form_units",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    slug: varchar({ length: 256 }).notNull(),
    form_id: int()
      .notNull()
      .references(() => forms.id, {
        onDelete: "cascade",
      }),
    label: varchar({ length: 256 }),
    // type: unit_types().default("text"),
    type: varchar({ length: 64 }).notNull(),
    index: int(),
    props: json().default(sql`'{}'::json`),
    require: boolean().default(false),
  },
  (t) => [index("units_slug_idx").on(t.slug)],
)

export const unit_relations = relations(units, ({ many, one }) => ({
  // values: many(values),
  form: one(forms, {
    fields: [units.form_id],
    references: [forms.id],
  }),
}))

export type Unit = typeof units.$inferSelect
export type NewUnit = typeof units.$inferInsert
export type UpdateUnit = Partial<Unit> & { id: number }

export const unit_zs = createSelectSchema(units)
export const unit_zi = createInsertSchema(units)
export const unit_zu = createUpdateSchema(units)
