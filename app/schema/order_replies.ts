import { relations, sql } from "drizzle-orm"
import { integer as int, pgTable as table, varchar, timestamp, text } from "drizzle-orm/pg-core"
import { orders } from "./orders"
import { users } from "./users"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"

export const order_replies = table("order_replies", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  order_id: int()
    .notNull()
    .references(() => orders.id, {
      onDelete: "cascade",
    }),
  creator_id: int().references(() => users.id, {
    onDelete: "set null",
  }),
  content: varchar({ length: 1024 }),
  images: text()
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`),
})

export const order_reply_relations = relations(order_replies, ({ one }) => ({
  order: one(orders, {
    fields: [order_replies.order_id],
    references: [orders.id],
  }),
  creator: one(users, {
    fields: [order_replies.creator_id],
    references: [users.id],
  }),
}))

export type OrderReply = typeof order_replies.$inferSelect
export type NewOrderReply = typeof order_replies.$inferInsert
export type UpdateOrderReply = Partial<OrderReply> & { id: number }

export const order_reply_zi = createInsertSchema(order_replies)
export const order_reply_zu = createUpdateSchema(order_replies)
