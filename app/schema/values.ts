// import { relations, sql } from "drizzle-orm"
// import { integer as int, pgTable as table, varchar as text } from "drizzle-orm/pg-core"
// import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
// import { submitings } from "./submitings"
// import { units } from "./units"

// export const values = table("submiting_values", {
//   id: int().primaryKey().generatedByDefaultAsIdentity(),
//   submiting_id: int()
//     .notNull()
//     .references(() => submitings.id, {
//       onDelete: "cascade",
//     }),
//   unit_id: int()
//     .notNull()
//     .references(() => units.id, {
//       onDelete: "cascade",
//     }),
//   value: text()
//     .array()
//     .notNull()
//     .default(sql`ARRAY[]::text[]`),
// })

// export const value_relations = relations(values, ({ one }) => ({
//   submiting: one(submitings, {
//     fields: [values.submiting_id],
//     references: [submitings.id],
//   }),
//   unit: one(units, {
//     fields: [values.unit_id],
//     references: [units.id],
//   }),
// }))

// export type Value = typeof values.$inferSelect
// export type NewValue = typeof values.$inferInsert
// export type UpdateValue = Partial<Value> & { id: number }

// export const value_zs = createSelectSchema(values)
// export const value_zi = createInsertSchema(values)
// export const value_zu = createUpdateSchema(values)
