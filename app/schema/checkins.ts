import { pgTable as table, integer as int, varchar, timestamp } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"
import { users } from "./users"
import { checkin_devices } from "./checkin_devices"

export const checkins = table("checkins", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).defaultNow(),
  device_id: varchar({ length: 64 })
    .references(() => checkin_devices.id, {
      onDelete: "restrict",
    })
    .notNull(),
  user_id: int().references(() => users.id, {
    onDelete: "cascade",
  }),
  project_id: int().references(() => projects.id, {
    onDelete: "cascade",
  }),
})

export type CheckIn = typeof checkins.$inferSelect
export type NewCheckIn = typeof checkins.$inferInsert
export type UpdateCheckIn = Partial<CheckIn> & { id: number }

export const checkin_zs = createSelectSchema(checkins)
export const checkin_zi = createInsertSchema(checkins)
export const checkin_zu = createUpdateSchema(checkins)
