import { relations } from "drizzle-orm"
import { integer as int, primaryKey, pgTable as table, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { materials } from "./materials"
import { cabinets } from "./cabinets"
import { projects } from "./projects"

export const cabinet_materials = table("cabinet_materials", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  material_id: int()
    .references(() => materials.id, {
      onDelete: "cascade",
    })
    .notNull(),
  cabinet_id: int()
    .references(() => cabinets.id, {
      onDelete: "cascade",
    })
    .notNull(),
  type: varchar({ length: 64 }).notNull(),
  start_index: int().notNull(),
  index: int().notNull(),
  direction: varchar({ length: 64 }),
  remark: varchar({ length: 1024 }),
})

export const cabinet_material_relations = relations(cabinet_materials, ({ one }) => ({
  material: one(materials, {
    fields: [cabinet_materials.material_id],
    references: [materials.id],
  }),
  cabinet: one(cabinets, {
    fields: [cabinet_materials.cabinet_id],
    references: [cabinets.id],
  }),
}))

export type CabinetMaterial = typeof cabinet_materials.$inferSelect
export type NewCabinetMaterial = typeof cabinet_materials.$inferInsert
export type UpdateCabinetMaterial = Partial<CabinetMaterial> & { id: number }

export const cabinet_material_zs = createSelectSchema(cabinet_materials)
export const cabinet_material_zi = createInsertSchema(cabinet_materials)
export const cabinet_material_zu = createUpdateSchema(cabinet_materials)
