import { pgEnum } from "drizzle-orm/pg-core"
import { createSelectSchema } from "drizzle-zod"
import { type z } from "zod"

export const point_types = pgEnum("point_types", ["frontend", "backend", "converge", "connect"])
// https://github.com/drizzle-team/drizzle-orm/discussions/1914#discussioncomment-10617667
// export type PointTypes = (typeof point_types.enumValues)[number]

export const point_type_z = createSelectSchema(point_types)
export type PointType = z.infer<typeof point_type_z>
