import { relations } from "drizzle-orm"
import { z } from "zod"
import { integer as int, pgTable as table, pgEnum } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema, createUpdateSchema } from "drizzle-zod"

import { projects } from "./projects"
import { submissions } from "./submissions"
import { ports } from "./ports"
// import { cabinet_materials } from "./cabinet_materials"

export const form_types = pgEnum("form_types", ["lane_setup", "device_setup", "other"])
export const form_type_z = createSelectSchema(form_types)
export type FormType = z.infer<typeof form_type_z>

export const links = table("links", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  project_id: int()
    .references(() => projects.id, {
      onDelete: "cascade",
    })
    .notNull(),
  device_submiting_id: int()
    .references(() => submissions.id, {
      onDelete: "cascade",
    })
    .notNull(),
  parent_device_submiting_id: int().references(() => submissions.id, {
    onDelete: "cascade",
  }),
  port_id: int()
    .references(() => ports.id, {
      onDelete: "cascade",
    })
    .notNull(),
  parent_port_id: int().references(() => ports.id, {
    onDelete: "cascade",
  }),
  line_submiting_id: int().references(() => submissions.id, {
    onDelete: "cascade",
  }),
  // cabinet_material_id: int()
  //   .references(() => cabinet_materials.id, {
  //     onDelete: "cascade",
  //   })
  //   .notNull(),
  group: int(),
  core: int(),
})

export const link_relations = relations(links, ({ many, one }) => ({
  project: one(projects, {
    fields: [links.project_id],
    references: [projects.id],
  }),
  device_submission: one(submissions, {
    fields: [links.device_submiting_id],
    references: [submissions.id],
  }),
  line_submission: one(submissions, {
    fields: [links.device_submiting_id],
    references: [submissions.id],
  }),
  port: one(ports, {
    fields: [links.port_id],
    references: [ports.id],
  }),
  // cabinet_material: one(cabinet_materials, {
  //   fields: [links.cabinet_material_id],
  //   references: [cabinet_materials.id],
  // }),
}))

export type Link = typeof links.$inferSelect
export type NewLink = typeof links.$inferInsert
export type UpdateLink = Partial<Link> & { id: number }

export const link_zi = createInsertSchema(links)
export const link_zu = createUpdateSchema(links)
