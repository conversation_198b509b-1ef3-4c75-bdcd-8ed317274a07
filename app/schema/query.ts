import { type Form } from "./forms"
import { type Unit } from "./units"
import { type Point } from "./points"
import { type Submission } from "./submissions"
import { type User } from "./users"
import { type IssueReply } from "./issue_replies"
import { type Issue } from "./issues"
import { OrderReply } from "./order_replies"
import { Order } from "./orders"

export type UserForQuery = Omit<
  User,
  "create_time" | "account_id" | "using" | "phone" | "identity" | "org" | "bankcard" | "gender" | "attendance"
>

export type SubmissionForQuery = Omit<Submission, "project_id" | "update_time" | "create_time" | "user_id">
export type SubmissionForQueryWith = SubmissionForQuery & {
  point: PointForQuery
  // user: UserForQuery
}

export type PointForQuery = Omit<Point, "parent_id" | "project_id" | "name" | "create_time" | "update_time">
export type PointParentForQuery = Omit<
  Point,
  "id" | "status" | "type" | "parent_id" | "project_id" | "name" | "create_time" | "update_time"
>
export type PointForQueryWith = PointForQuery & {
  parent?: PointParentForQuery
}
export type PointForQueryWithValues = PointForQueryWith & {
  values?: unknown
}

export type FormForQuery = Omit<Form, "index" | "icon" | "project_id" | "singleton_submiting">
export type UnitForQuery = Omit<Unit, "form_id" | "props" | "require" | "index" | "type">
export type FormForQueryWith = FormForQuery & {
  units: UnitForQuery[]
}
export type FormForQueryLiteWith = { id: number } & {
  units: UnitForQuery[]
}

export type IssueForQuery = Omit<Issue, "project_id" | "point_ids" | "asker_id" | "solver_id">
export type IssueReplyForQuery = Omit<IssueReply, "issue_id" | "creator_id">
export type IssueForQueryWith = IssueForQuery & {
  points: PointForQuery[]
  replies: IssueReplyForQuery[]
}

export type OrderForQuery = Omit<Order, "project_id" | "point_id" | "creator_id" | "handler_id">
export type OrderReplyForQuery = Omit<OrderReply, "order_id" | "creator_id">
export type OrderForQueryWith = OrderForQuery & {
  points: PointForQuery[]
  replies: OrderReplyForQuery[]
}
