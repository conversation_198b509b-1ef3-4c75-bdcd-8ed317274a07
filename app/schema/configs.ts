import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, index, varchar, type AnyPgColumn } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { config_cates } from "./config_cates"

export const configs = table("configs", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  cate_id: int()
    .references(() => config_cates.id, {
      onDelete: "cascade",
    })
    .notNull(),
  code: varchar({ length: 64 }).notNull(),
  name: varchar({ length: 256 }).notNull(),
  name_en: varchar({ length: 256 }),
  name_en_short: varchar({ length: 128 }),
  description: varchar({ length: 1024 }),
  parent_id: int().references((): AnyPgColumn => configs.id, {
    onDelete: "set null",
  }),
  index: int(),
})

export const configs_relations = relations(configs, ({ one }) => ({
  cate: one(config_cates, {
    fields: [configs.cate_id],
    references: [config_cates.id],
  }),
  parent: one(configs, {
    fields: [configs.parent_id],
    references: [configs.id],
  }),
}))

export type Config = typeof configs.$inferSelect
export type NewConfig = typeof configs.$inferInsert
export type UpdateConfig = Partial<Config> & { id: number }

export const config_zs = createSelectSchema(configs)
export const config_zi = createInsertSchema(configs)
export const config_zu = createUpdateSchema(configs)
