import { relations } from "drizzle-orm"
import { integer as int, pgTable as table, timestamp, varchar } from "drizzle-orm/pg-core"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { z } from "zod"

import { users, type NewUser, type User } from "./users"

export const accounts = table("accounts", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  name: varchar({ length: 64 }).notNull().unique(),
  password: varchar({ length: 64 }).notNull(),
})

export const account_relations = relations(accounts, ({ many }) => ({
  users: many(users),
}))

export type NativeAccount = typeof accounts.$inferSelect
export type Account = Omit<NativeAccount, "password"> & { users?: User[] }
export type NewAccount = typeof accounts.$inferInsert & { users?: NewUser[] }
export type UpdateAccount = Partial<Account> & { id: number }

export const account_zi = createInsertSchema(accounts) // signUp replace with models/accounts.server.ts:account_user_zi
export const account_zu = createUpdateSchema(accounts, {
  password: z.undefined(),
})
