import { integer as int, pgTable as table, timestamp, varchar } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"

import { point_types } from "./point_types"
import { point_status } from "./points"

export const point_logs = table("point_logs", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  point_id: int().notNull(),
  submitter_name: varchar({ length: 256 }).notNull(),
  create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  project_id: int().notNull(),
  name: varchar({ length: 256 }).notNull(),
  slug: varchar({ length: 256 }).notNull(),
  type: point_types().notNull(),
  status: point_status().notNull(),
  parent_id: int(),
})

export type PointLog = typeof point_logs.$inferSelect
export type NewPointLog = typeof point_logs.$inferInsert
export type UpdatePoint = Partial<PointLog> & { id: number }

export const pointlog_zs = createSelectSchema(point_logs)
export const pointlog_zi = createInsertSchema(point_logs)
export const pointlog_zu = createUpdateSchema(point_logs)
