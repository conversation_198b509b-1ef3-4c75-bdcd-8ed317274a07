import {
  integer as int,
  real,
  pgTable as table,
  index,
  varchar,
  boolean,
  timestamp,
  text,
} from "drizzle-orm/pg-core"
import { createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { projects } from "./projects"
import { relations, sql } from "drizzle-orm"

export const materials = table(
  "materials",
  {
    id: int().primaryKey().generatedByDefaultAsIdentity(),
    project_id: int()
      .references(() => projects.id, {
        onDelete: "cascade",
      })
      .notNull(),
    code: varchar({ length: 256 }).notNull(),
    name: varchar({ length: 256 }).notNull(),
    model: varchar({ length: 256 }),
    tax_rate: real(),
    brand: varchar({ length: 64 }),
    create_time: timestamp({ mode: "string" }).notNull().defaultNow(),
    measure_unit: varchar({ length: 64 }).notNull(),
    is_outsourcing: boolean().default(false),
    is_sale: boolean().default(false),
    is_homemade: boolean().default(false),
    is_consumable: boolean().default(false),
    is_labor: boolean().default(false),
    is_ecommerce: boolean().default(false),
    is_from_oa: boolean().default(false),
    description: varchar({ length: 1024 }),
    source: varchar({ length: 256 }),
    subsystem: varchar({ length: 256 }),
    manifest: varchar({ length: 256 }),
    is_reference: boolean().notNull().default(false),
    depth: real(),
    width: real(),
    height: real(),
    power: real(),
    weight: real(),
    images: text()
      .array()
      .notNull()
      .default(sql`ARRAY[]::text[]`),
    device_type: varchar({ length: 64 }),
    device_type_en: varchar({ length: 64 }),
  },
  (t) => [index("materials_code_idx").on(t.code)],
)

export const material_relations = relations(materials, ({ one }) => ({
  project: one(projects, {
    fields: [materials.project_id],
    references: [projects.id],
  }),
}))

export type Material = typeof materials.$inferSelect & {
  front?: boolean
  opposite?: boolean
}
export type NewMaterial = typeof materials.$inferInsert
export type UpdateMaterial = Partial<Material> & { id: number }

export const material_zi = createInsertSchema(materials)
export const material_zu = createUpdateSchema(materials)
