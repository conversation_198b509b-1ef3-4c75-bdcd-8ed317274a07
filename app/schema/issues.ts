import { relations, sql } from "drizzle-orm"
import { integer as int, pgTable as table, varchar, AnyPgColumn, timestamp, text } from "drizzle-orm/pg-core"
import { createSelectSchema, createInsertSchema, createUpdateSchema } from "drizzle-zod"
import { users } from "./users"
import { projects } from "./projects"
import { issue_replies } from "./issue_replies"

export const issues = table("point_issues", {
  id: int().primaryKey().generatedByDefaultAsIdentity(),
  project_id: int()
    .notNull()
    .references((): AnyPgColumn => projects.id, {
      onDelete: "cascade",
    }),
  point_ids: int()
    .array()
    .notNull()
    .default(sql`ARRAY[]::integer[]`),
  description: varchar({ length: 1024 }).notNull(),
  attachments: text()
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`),
  asker_id: int()
    .references(() => users.id, {
      onDelete: "set null",
    })
    .notNull(),
  ask_time: timestamp({ mode: "string" }).notNull().defaultNow(),
  solution: varchar({ length: 1024 }),
  solver_id: int().references(() => users.id, {
    onDelete: "set null",
  }),
  solution_time: timestamp({ mode: "string" }),
  system: varchar({ length: 64 }),
  sub_company: varchar({ length: 64 }),
  location1: varchar({ length: 64 }),
  location2: varchar({ length: 64 }),
  area_des: varchar({ length: 256 }),
  responsible_party: varchar({ length: 256 }),
})

export const issues_relations = relations(issues, ({ one, many }) => ({
  project: one(projects, {
    fields: [issues.project_id],
    references: [projects.id],
  }),
  asker: one(users, {
    fields: [issues.asker_id],
    references: [users.id],
  }),
  solver: one(users, {
    fields: [issues.solver_id],
    references: [users.id],
  }),
  replies: many(issue_replies),
}))

export type Issue = typeof issues.$inferSelect
export type NewIssue = typeof issues.$inferInsert
export type UpdateIssue = Partial<Issue> & { id: number }

export const issus_zs = createSelectSchema(issues)
export const issus_zi = createInsertSchema(issues)
export const issus_zu = createUpdateSchema(issues)
