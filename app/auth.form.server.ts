import "dotenv/config"
import { createCookieSessionStorage } from "@remix-run/node"
import { Authenticator, AuthorizationError } from "remix-auth"
import { FormStrategy } from "remix-auth-form"
import invariant from "tiny-invariant"

import { HttpStatusCode as Status } from "~/utils/http_code"
import { signIn } from "~/models/accounts.server"
import { Account } from "./schema/accounts"

invariant(process.env.SESSION_SECRET, "SESSION_SECRET must be set")

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    httpOnly: true,
    path: "/",
    sameSite: "lax",
    secrets: [process.env.SESSION_SECRET],
    // secure: process.env.NODE_ENV === "production",
    secure: false,
  },
})

export const auth = new Authenticator<Account>(sessionStorage)

auth.use(
  new FormStrategy(async ({ form }) => {
    const name = form.get("name") as string | null
    const password = form.get("password") as string | null
    if (!name || !password) throw new AuthorizationError("invalid credentials")

    return await signIn(name, password)
  }),
)

export const isAuthenticated = async (request: Request): Promise<Account> => {
  const account = await auth.isAuthenticated(request)
  if (account == null) throw new Response("unauthorized", { status: Status.Unauthorized })
  // console.log("account", account)
  return account
}
