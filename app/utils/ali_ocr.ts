import Client, { RecognizeGeneralRequest, RecognizeGeneralResponseBody } from "@alicloud/ocr-api20210707"
import { $OpenApiUtil } from "@alicloud/openapi-core"

const config = new $OpenApiUtil.Config({
  endpoint: "ocr-api.cn-hangzhou.aliyuncs.com",
  regionId: "cn-hangzhou",
  accessKeyId: "LTAI5tKd3wezKbaDDjUE9tXM",
  accessKeySecret: "******************************",
})

const client = new Client(config)

const recognize = async (url: string): Promise<RecognizeGeneralResponseBody | undefined> => {
  const request = new RecognizeGeneralRequest({ url })

  try {
    const response = await client.recognizeGeneral(request)
    return response.body
  } catch (error) {
    console.error("ali-ocr error", error)
  }
}

export default recognize
