import sharp from "sharp"

export interface ImageInfo {
  buffer: Buffer
  width: number
  height: number
  isHorizontal: boolean
}

export interface TextConfig {
  text: string
  width: number
  height: number
  fontSize: number
}

export interface CompositionResult {
  buffer: Buffer
  width: number
  height: number
}

/**
 * Fetch image from URL and get its metadata
 */
export async function fetchImageInfo(url: string): Promise<ImageInfo> {
  try {
    // Validate URL format
    const urlObj = new URL(url)
    if (!["http:", "https:"].includes(urlObj.protocol)) {
      throw new Error(`Unsupported protocol: ${urlObj.protocol}`)
    }

    const response = await fetch(url, {
      headers: {
        "User-Agent": "Image-Compose-Service/1.0",
      },
      // Add timeout
      signal: AbortSignal.timeout(30000), // 30 seconds
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch image from ${url}: ${response.status} ${response.statusText}`)
    }

    // Check content type
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.startsWith("image/")) {
      throw new Error(`Invalid content type: ${contentType}. Expected image/*`)
    }

    const arrayBuffer = await response.arrayBuffer()

    // Check file size (max 50MB)
    if (arrayBuffer.byteLength > 50 * 1024 * 1024) {
      throw new Error(`Image too large: ${arrayBuffer.byteLength} bytes. Maximum allowed: 50MB`)
    }

    const buffer = Buffer.from(arrayBuffer)

    const image = sharp(buffer)
    const metadata = await image.metadata()

    if (!metadata.width || !metadata.height) {
      throw new Error(
        `Invalid image metadata from ${url}: width=${metadata.width}, height=${metadata.height}`,
      )
    }

    // Check minimum dimensions
    if (metadata.width < 10 || metadata.height < 10) {
      throw new Error(`Image too small: ${metadata.width}x${metadata.height}. Minimum: 10x10`)
    }

    // Check maximum dimensions
    if (metadata.width > 10000 || metadata.height > 10000) {
      throw new Error(`Image too large: ${metadata.width}x${metadata.height}. Maximum: 10000x10000`)
    }

    return {
      buffer,
      width: metadata.width,
      height: metadata.height,
      isHorizontal: metadata.width > metadata.height,
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Unexpected error fetching image from ${url}: ${String(error)}`)
  }
}

/**
 * Determine layout type based on image orientations
 */
export function determineLayoutType(
  imageA: ImageInfo,
  imageB: ImageInfo,
): "both-horizontal" | "both-vertical" | "mixed" {
  if (imageA.isHorizontal && imageB.isHorizontal) {
    return "both-horizontal"
  } else if (!imageA.isHorizontal && !imageB.isHorizontal) {
    return "both-vertical"
  } else {
    return "mixed"
  }
}

/**
 * Scale image proportionally to fit target dimensions
 */
export async function scaleImage(
  imageInfo: ImageInfo,
  targetWidth: number,
  targetHeight?: number,
): Promise<Buffer> {
  let resizeOptions: { width?: number; height?: number; fit?: keyof sharp.FitEnum } = {
    width: targetWidth,
    fit: "inside",
  }

  if (targetHeight) {
    resizeOptions.height = targetHeight
  }

  return await sharp(imageInfo.buffer).resize(resizeOptions).toBuffer()
}

/**
 * Break text into lines based on available width and font size
 */
function breakTextIntoLines(text: string, availableWidth: number, fontSize: number): string[] {
  const words = text.split(" ")
  const charWidth = fontSize * 0.6 // Approximate character width
  const charsPerLine = Math.floor((availableWidth - 20) / charWidth) // Account for padding

  const lines: string[] = []
  let currentLine = ""

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word
    if (testLine.length <= charsPerLine) {
      currentLine = testLine
    } else {
      if (currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        // Word is too long, break it
        lines.push(word.substring(0, charsPerLine))
        currentLine = word.substring(charsPerLine)
      }
    }
  }
  if (currentLine) {
    lines.push(currentLine)
  }

  return lines
}

/**
 * Break text into lines by wrapping at every space (word wrapping)
 * Used specifically for Layout 3 where we need precise control over line count
 */
function breakTextByWords(text: string, availableWidth: number, fontSize: number): string[] {
  const words = text.split(" ")
  const charWidth = fontSize * 0.6 // Approximate character width
  const maxLineWidth = availableWidth - 20 // Account for padding

  const lines: string[] = []
  let currentLine = ""

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word
    const testLineWidth = testLine.length * charWidth

    if (testLineWidth <= maxLineWidth) {
      currentLine = testLine
    } else {
      if (currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        // Single word is too long, force it on its own line
        lines.push(word)
        currentLine = ""
      }
    }
  }
  if (currentLine) {
    lines.push(currentLine)
  }

  return lines
}

/**
 * Calculate text config specifically for Layout 3 (mixed orientations)
 * Font size is determined by the number of lines needed to fit all text
 */
export function calculateTextConfigForMixed(
  text: string,
  availableWidth: number,
  availableHeight: number,
): TextConfig {
  const lineHeightMultiplier = 1.2
  const padding = 20 // Total vertical padding
  const availableTextHeight = availableHeight - padding

  // Start with a reasonable font size and calculate how many lines we need
  let fontSize = Math.max(12, Math.min(48, Math.floor(availableWidth / 25)))

  // First, determine how many lines we need with word wrapping
  const lines = breakTextByWords(text, availableWidth, fontSize)
  const requiredLines = lines.length

  // Calculate the maximum font size that allows all lines to fit
  const maxFontSizeForHeight = availableTextHeight / (requiredLines * lineHeightMultiplier)

  // Use the smaller of the two constraints
  fontSize = Math.min(fontSize, maxFontSizeForHeight)

  // Ensure minimum readable font size
  fontSize = Math.max(8, fontSize)

  return {
    text,
    width: availableWidth,
    height: availableHeight,
    fontSize: Math.floor(fontSize),
  }
}

/**
 * Calculate text dimensions and font size based on available space
 */
export function calculateTextConfig(
  text: string,
  availableWidth: number,
  availableHeight?: number,
): TextConfig {
  // Start with a reasonable font size
  let fontSize = Math.max(16, Math.min(72, Math.floor(availableWidth / 20)))
  const lineHeightMultiplier = 1.2
  const padding = 20 // Total vertical padding

  // If no height constraint, use the initial font size
  if (!availableHeight) {
    const lines = breakTextIntoLines(text, availableWidth, fontSize)
    const textHeight = Math.ceil(lines.length * fontSize * lineHeightMultiplier + padding)

    return {
      text,
      width: availableWidth,
      height: textHeight,
      fontSize: Math.floor(fontSize),
    }
  }

  // If height constraint exists, iteratively find the best font size
  const maxIterations = 20
  let iteration = 0

  while (iteration < maxIterations) {
    const lines = breakTextIntoLines(text, availableWidth, fontSize)
    const requiredHeight = lines.length * fontSize * lineHeightMultiplier + padding

    if (requiredHeight <= availableHeight) {
      // Text fits! Return the configuration
      return {
        text,
        width: availableWidth,
        height: availableHeight,
        fontSize: Math.floor(fontSize),
      }
    }

    // Text doesn't fit, reduce font size
    fontSize = fontSize * 0.9

    // Minimum font size check
    if (fontSize < 8) {
      console.warn(`Text too long to fit in available space. Using minimum font size.`)
      return {
        text,
        width: availableWidth,
        height: availableHeight,
        fontSize: 8,
      }
    }

    iteration++
  }

  // Fallback if we couldn't find a good fit
  return {
    text,
    width: availableWidth,
    height: availableHeight,
    fontSize: Math.floor(fontSize),
  }
}

/**
 * Create text image with black background and white text
 */
export async function createTextImage(
  textConfig: TextConfig,
  useWordWrapping: boolean = false,
): Promise<Buffer> {
  const { text, width, height, fontSize } = textConfig

  // Choose line breaking method based on the flag
  const lines = useWordWrapping
    ? breakTextByWords(text, width, fontSize)
    : breakTextIntoLines(text, width, fontSize)

  const lineHeight = fontSize * 1.2
  const padding = 10

  // Ensure we don't exceed the available height
  const maxLines = Math.floor((height - padding * 2) / lineHeight)
  const displayLines = lines.slice(0, maxLines)

  // If we had to truncate lines, add ellipsis to the last line
  if (lines.length > maxLines && maxLines > 0) {
    const lastLine = displayLines[displayLines.length - 1]
    const charWidth = fontSize * 0.6
    const maxCharsInLastLine = Math.floor((width - padding * 2) / charWidth) - 3 // Reserve space for "..."

    if (lastLine.length > maxCharsInLastLine) {
      displayLines[displayLines.length - 1] = lastLine.substring(0, maxCharsInLastLine) + "..."
    } else {
      displayLines[displayLines.length - 1] = lastLine + "..."
    }
  }

  // Create SVG for text rendering
  const svgHeight = height

  let svgContent = `<svg width="${width}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="black"/>
    <style>
      .text {
        font-family: Arial, sans-serif;
        font-size: ${fontSize}px;
        fill: white;
        dominant-baseline: hanging;
      }
    </style>`

  // Add text lines
  displayLines.forEach((line, index) => {
    const y = padding + index * lineHeight
    svgContent += `<text x="${padding}" y="${y}" class="text">${escapeXml(line)}</text>`
  })

  svgContent += "</svg>"

  // Convert SVG to image buffer
  return await sharp(Buffer.from(svgContent)).png().toBuffer()
}

/**
 * Escape XML special characters
 */
function escapeXml(text: string): string {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
}

/**
 * Layout 1: Both images horizontal - Text + Image A + Image B (vertical stack)
 */
export async function layoutBothHorizontal(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine the width based on the higher resolution image
  const maxWidth = Math.max(imageA.width, imageB.width)

  // Scale images to match the max width
  const scaledImageA = await scaleImage(imageA, maxWidth)
  const scaledImageB = await scaleImage(imageB, maxWidth)

  // Get scaled image dimensions
  const metadataA = await sharp(scaledImageA).metadata()
  const metadataB = await sharp(scaledImageB).metadata()

  // Calculate text configuration
  const textConfig = calculateTextConfig(text, maxWidth)
  const textImage = await createTextImage(textConfig)

  // Calculate total height
  const totalHeight = textConfig.height + (metadataA.height || 0) + (metadataB.height || 0)

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledImageA, top: textConfig.height, left: 0 },
    { input: scaledImageB, top: textConfig.height + (metadataA.height || 0), left: 0 },
  ]

  const result = await sharp({
    create: {
      width: maxWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: maxWidth,
    height: totalHeight,
  }
}

/**
 * Layout 2: Both images vertical - Text top, Image A + Image B side-by-side bottom
 */
export async function layoutBothVertical(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine the width based on the higher resolution image
  const maxImageWidth = Math.max(imageA.width, imageB.width)
  const totalWidth = maxImageWidth * 2

  // Scale images to half the total width each
  const targetImageWidth = maxImageWidth
  const scaledImageA = await scaleImage(imageA, targetImageWidth)
  const scaledImageB = await scaleImage(imageB, targetImageWidth)

  // Get scaled image dimensions
  const metadataA = await sharp(scaledImageA).metadata()
  const metadataB = await sharp(scaledImageB).metadata()

  const imageHeight = Math.max(metadataA.height || 0, metadataB.height || 0)

  // Calculate text configuration
  const textConfig = calculateTextConfig(text, totalWidth)
  const textImage = await createTextImage(textConfig)

  // Calculate total height
  const totalHeight = textConfig.height + imageHeight

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledImageA, top: textConfig.height, left: 0 },
    { input: scaledImageB, top: textConfig.height, left: targetImageWidth },
  ]

  const result = await sharp({
    create: {
      width: totalWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: totalWidth,
    height: totalHeight,
  }
}

/**
 * Layout 3: Mixed orientations - Text + vertical image side-by-side top, horizontal image bottom
 */
export async function layoutMixed(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine which image is vertical and which is horizontal
  const verticalImage = imageA.isHorizontal ? imageB : imageA
  const horizontalImage = imageA.isHorizontal ? imageA : imageB

  // The vertical image width should be 1/2 the width of the horizontal image
  const horizontalWidth = horizontalImage.width
  const verticalTargetWidth = Math.floor(horizontalWidth / 2)

  // Scale images
  const scaledHorizontal = await scaleImage(horizontalImage, horizontalWidth)
  const scaledVertical = await scaleImage(verticalImage, verticalTargetWidth)

  // Get scaled image dimensions
  const horizontalMetadata = await sharp(scaledHorizontal).metadata()
  const verticalMetadata = await sharp(scaledVertical).metadata()

  const horizontalHeight = horizontalMetadata.height || 0
  const verticalHeight = verticalMetadata.height || 0

  // Calculate text area dimensions (remaining space next to vertical image)
  const textWidth = horizontalWidth - verticalTargetWidth
  const upperSectionHeight = verticalHeight

  // Use the specialized text configuration for Layout 3 (mixed orientations)
  const textConfig = calculateTextConfigForMixed(text, textWidth, upperSectionHeight)
  const textImage = await createTextImage(textConfig, true) // Use word wrapping

  // Calculate total dimensions
  const totalWidth = horizontalWidth
  const totalHeight = upperSectionHeight + horizontalHeight

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledVertical, top: 0, left: textWidth },
    { input: scaledHorizontal, top: upperSectionHeight, left: 0 },
  ]

  const result = await sharp({
    create: {
      width: totalWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: totalWidth,
    height: totalHeight,
  }
}

/**
 * Validate text input
 */
function validateText(text: string): void {
  if (!text || typeof text !== "string") {
    throw new Error("Text must be a non-empty string")
  }

  if (text.length > 5000) {
    throw new Error("Text too long. Maximum length: 5000 characters")
  }

  // Remove excessive whitespace but preserve intentional spaces
  const cleanedText = text.trim()
  if (!cleanedText) {
    throw new Error("Text cannot be empty or only whitespace")
  }
}

/**
 * Main function to compose text and two images according to layout rules
 */
export async function composeImages(
  text: string,
  imageUrlA: string,
  imageUrlB: string,
): Promise<CompositionResult> {
  // Validate inputs
  validateText(text)

  if (!imageUrlA || !imageUrlB) {
    throw new Error("Both image URLs must be provided")
  }

  if (imageUrlA === imageUrlB) {
    throw new Error("Image URLs must be different")
  }

  try {
    // Fetch both images
    const [imageA, imageB] = await Promise.all([fetchImageInfo(imageUrlA), fetchImageInfo(imageUrlB)])

    // Determine layout type
    const layoutType = determineLayoutType(imageA, imageB)

    // Apply appropriate layout
    switch (layoutType) {
      case "both-horizontal":
        return await layoutBothHorizontal(text, imageA, imageB)
      case "both-vertical":
        return await layoutBothVertical(text, imageA, imageB)
      case "mixed":
        return await layoutMixed(text, imageA, imageB)
      default:
        throw new Error(`Unknown layout type: ${layoutType}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Image composition failed: ${String(error)}`)
  }
}
