import sharp from "sharp"

export interface ImageInfo {
  buffer: Buffer
  width: number
  height: number
  isHorizontal: boolean
}

export interface TextConfig {
  text: string
  width: number
  height: number
  fontSize: number
}

export interface TextProcessingOptions {
  preserveSpaces: boolean
  layoutType: "layout1-2" | "layout3"
  mainImageWidth: number
  mainImageHeight: number
}

export interface CompositionResult {
  buffer: Buffer
  width: number
  height: number
}

/**
 * Fetch image from URL and get its metadata
 */
export async function fetchImageInfo(url: string): Promise<ImageInfo> {
  try {
    // Validate URL format
    const urlObj = new URL(url)
    if (!["http:", "https:"].includes(urlObj.protocol)) {
      throw new Error(`Unsupported protocol: ${urlObj.protocol}`)
    }

    const response = await fetch(url, {
      headers: {
        "User-Agent": "Image-Compose-Service/1.0",
      },
      // Add timeout
      signal: AbortSignal.timeout(30000), // 30 seconds
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch image from ${url}: ${response.status} ${response.statusText}`)
    }

    // Check content type
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.startsWith("image/")) {
      throw new Error(`Invalid content type: ${contentType}. Expected image/*`)
    }

    const arrayBuffer = await response.arrayBuffer()

    // Check file size (max 50MB)
    if (arrayBuffer.byteLength > 50 * 1024 * 1024) {
      throw new Error(`Image too large: ${arrayBuffer.byteLength} bytes. Maximum allowed: 50MB`)
    }

    const buffer = Buffer.from(arrayBuffer)

    const image = sharp(buffer)
    const metadata = await image.metadata()

    if (!metadata.width || !metadata.height) {
      throw new Error(
        `Invalid image metadata from ${url}: width=${metadata.width}, height=${metadata.height}`,
      )
    }

    // Check minimum dimensions
    if (metadata.width < 10 || metadata.height < 10) {
      throw new Error(`Image too small: ${metadata.width}x${metadata.height}. Minimum: 10x10`)
    }

    // Check maximum dimensions
    if (metadata.width > 10000 || metadata.height > 10000) {
      throw new Error(`Image too large: ${metadata.width}x${metadata.height}. Maximum: 10000x10000`)
    }

    return {
      buffer,
      width: metadata.width,
      height: metadata.height,
      isHorizontal: metadata.width > metadata.height,
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Unexpected error fetching image from ${url}: ${String(error)}`)
  }
}

/**
 * Determine layout type based on image orientations
 */
export function determineLayoutType(
  imageA: ImageInfo,
  imageB: ImageInfo,
): "both-horizontal" | "both-vertical" | "mixed" {
  if (imageA.isHorizontal && imageB.isHorizontal) {
    return "both-horizontal"
  } else if (!imageA.isHorizontal && !imageB.isHorizontal) {
    return "both-vertical"
  } else {
    return "mixed"
  }
}

/**
 * Scale image proportionally to fit target dimensions
 */
export async function scaleImage(
  imageInfo: ImageInfo,
  targetWidth: number,
  targetHeight?: number,
): Promise<Buffer> {
  let resizeOptions: { width?: number; height?: number; fit?: keyof sharp.FitEnum } = {
    width: targetWidth,
    fit: "inside",
  }

  if (targetHeight) {
    resizeOptions.height = targetHeight
  }

  return await sharp(imageInfo.buffer).resize(resizeOptions).toBuffer()
}

/**
 * Calculate optimal font size based on main image dimensions
 */
function calculateBaseFontSize(mainImageWidth: number, mainImageHeight: number): number {
  // Base font size calculation using image dimensions
  const imageDiagonal = Math.sqrt(mainImageWidth * mainImageWidth + mainImageHeight * mainImageHeight)
  const baseFontSize = Math.max(12, Math.min(72, imageDiagonal / 50))
  return baseFontSize
}

/**
 * Normalize text by preserving multiple spaces and handling whitespace properly
 */
function normalizeTextWithSpaces(text: string): string {
  // Trim leading/trailing whitespace but preserve internal multiple spaces
  return text.trim().replace(/\s+/g, " ")
}

/**
 * Calculate character width considering mixed Chinese/English text
 */
function calculateMixedCharWidth(text: string, fontSize: number): number {
  // Chinese characters are typically wider than English characters
  const chineseCharRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/
  const hasChineseChars = chineseCharRegex.test(text)

  if (hasChineseChars) {
    // Conservative estimate for mixed text: Chinese chars ~1.0x fontSize, English ~0.6x
    // Use a weighted average leaning towards wider characters
    return fontSize * 0.8
  } else {
    // Pure English text
    return fontSize * 0.6
  }
}

/**
 * Calculate actual text width more accurately for mixed Chinese/English
 */
function calculateTextWidth(text: string, fontSize: number): number {
  const chineseCharRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/
  let totalWidth = 0

  for (const char of text) {
    if (chineseCharRegex.test(char)) {
      totalWidth += fontSize * 1.0 // Chinese characters are roughly 1x fontSize wide
    } else {
      totalWidth += fontSize * 0.6 // English characters are roughly 0.6x fontSize wide
    }
  }

  return totalWidth
}

/**
 * Break text into lines for Layout 1 & 2 - conservative approach for complete text display
 * Line breaks are determined by text area width, with early breaking to ensure completeness
 */
function breakTextForLayout12(
  text: string,
  availableWidth: number,
  fontSize: number,
  preserveSpaces: boolean = true,
): string[] {
  const normalizedText = preserveSpaces ? normalizeTextWithSpaces(text) : text
  const words = normalizedText.split(" ")
  const padding = 20
  const maxLineWidth = availableWidth - padding

  // Use conservative safety margin (15% less than available width)
  const safeLineWidth = maxLineWidth * 0.85

  const lines: string[] = []
  let currentLine = ""

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word
    const testLineWidth = calculateTextWidth(testLine, fontSize)

    // Conservative approach: break early if we're approaching the limit
    if (testLineWidth <= safeLineWidth) {
      currentLine = testLine
    } else {
      // Current line would be too long, start a new line
      if (currentLine) {
        lines.push(currentLine)
        currentLine = word

        // Check if the single word itself is too long
        const singleWordWidth = calculateTextWidth(word, fontSize)
        if (singleWordWidth > safeLineWidth) {
          // Force break long words conservatively
          const avgCharWidth = calculateMixedCharWidth(word, fontSize)
          const maxCharsPerLine = Math.floor(safeLineWidth / avgCharWidth)

          if (maxCharsPerLine > 0) {
            lines.push(word.substring(0, maxCharsPerLine))
            currentLine = word.substring(maxCharsPerLine)
          } else {
            // Extremely narrow space, just put the word as is
            lines.push(word)
            currentLine = ""
          }
        }
      } else {
        // This shouldn't happen in normal cases, but handle it
        currentLine = word
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine)
  }

  return lines
}

/**
 * Break text into lines for Layout 3 - wraps at every space
 * Each word goes on its own line (line break at every space)
 */
function breakTextForLayout3(text: string, preserveSpaces: boolean = true): string[] {
  const normalizedText = preserveSpaces ? normalizeTextWithSpaces(text) : text
  const words = normalizedText.split(" ")

  // For Layout 3, each word gets its own line (wrap at every space)
  // Filter out empty strings that might result from multiple spaces
  return words.filter((word) => word.length > 0)
}

/**
 * Calculate text config specifically for Layout 3 (mixed orientations)
 * Font size is determined by the number of lines (words) needed to fit all text
 */
export function calculateTextConfigForMixed(
  text: string,
  availableWidth: number,
  availableHeight: number,
  mainImageWidth: number,
  mainImageHeight: number,
): TextConfig {
  const lineHeightMultiplier = 1.2
  const padding = 20 // Total vertical padding
  const availableTextHeight = availableHeight - padding

  // For Layout 3, each word is on its own line, so count words
  const normalizedText = normalizeTextWithSpaces(text)
  const words = normalizedText.split(" ").filter((word) => word.length > 0)
  const requiredLines = words.length

  // Calculate the maximum font size that allows all lines to fit
  const maxFontSizeForHeight = availableTextHeight / (requiredLines * lineHeightMultiplier)

  // Calculate base font size from main image dimensions as a starting point
  const baseFontSize = calculateBaseFontSize(mainImageWidth, mainImageHeight)

  // Font size is primarily determined by the number of lines (words)
  let fontSize = Math.min(baseFontSize, maxFontSizeForHeight)

  // Ensure minimum readable font size
  fontSize = Math.max(8, fontSize)

  return {
    text,
    width: availableWidth,
    height: availableHeight,
    fontSize: Math.floor(fontSize),
  }
}

/**
 * Calculate text dimensions and font size based on available space for Layout 1 & 2
 * Uses conservative approach to ensure complete text display
 */
export function calculateTextConfig(
  text: string,
  availableWidth: number,
  mainImageWidth: number,
  mainImageHeight: number,
  availableHeight?: number,
): TextConfig {
  // Calculate base font size from main image dimensions
  const baseFontSize = calculateBaseFontSize(mainImageWidth, mainImageHeight)
  let fontSize = Math.max(16, Math.min(72, baseFontSize))
  const lineHeightMultiplier = 1.3 // Slightly more generous line height
  const padding = 30 // More generous padding for better readability

  // If no height constraint, use conservative approach
  if (!availableHeight) {
    // Start with a conservative font size estimate
    fontSize = Math.min(fontSize, baseFontSize * 0.8)

    const lines = breakTextForLayout12(text, availableWidth, fontSize, true)
    const textHeight = Math.ceil(lines.length * fontSize * lineHeightMultiplier + padding)

    return {
      text,
      width: availableWidth,
      height: textHeight,
      fontSize: Math.floor(fontSize),
    }
  }

  // If height constraint exists, use more conservative iterative approach
  const maxIterations = 30 // More iterations for better precision
  let iteration = 0
  let bestFontSize = fontSize
  let bestFit = false

  // Start with a smaller font size to be more conservative
  fontSize = Math.min(fontSize, baseFontSize * 0.7)

  while (iteration < maxIterations) {
    const lines = breakTextForLayout12(text, availableWidth, fontSize, true)
    const requiredHeight = lines.length * fontSize * lineHeightMultiplier + padding

    // Add safety margin - require 10% extra space
    const safeRequiredHeight = requiredHeight * 1.1

    if (safeRequiredHeight <= availableHeight) {
      // Text fits with safety margin!
      bestFontSize = fontSize
      bestFit = true

      // Try slightly larger font size to optimize
      fontSize = fontSize * 1.05
    } else {
      // Text doesn't fit, reduce font size more aggressively
      fontSize = fontSize * 0.85
    }

    // Minimum font size check
    if (fontSize < 8) {
      if (!bestFit) {
        console.warn(`Text too long to fit in available space. Using minimum font size.`)
        bestFontSize = 8
      }
      break
    }

    iteration++
  }

  return {
    text,
    width: availableWidth,
    height: availableHeight,
    fontSize: Math.floor(bestFontSize),
  }
}

/**
 * Create text image with black background and white text
 * Uses conservative approach to ensure all text is properly displayed
 */
export async function createTextImage(
  textConfig: TextConfig,
  layoutType: "layout1-2" | "layout3" = "layout1-2",
): Promise<Buffer> {
  const { text, width, height, fontSize } = textConfig

  // Choose line breaking method based on layout type
  const lines =
    layoutType === "layout3"
      ? breakTextForLayout3(text, true)
      : breakTextForLayout12(text, width, fontSize, true)

  const lineHeight = fontSize * 1.3 // More generous line height
  const padding = 15 // More generous padding

  // Conservative approach: ensure we have enough space for all lines
  const availableTextHeight = height - padding * 2
  const maxLines = Math.floor(availableTextHeight / lineHeight)

  // If we have more lines than can fit, we need to be more aggressive about truncation
  let displayLines = lines.slice(0, maxLines)

  // Only add ellipsis if we actually truncated content
  if (lines.length > maxLines && maxLines > 0) {
    const lastLine = displayLines[displayLines.length - 1]

    // Conservative ellipsis calculation considering mixed text
    const avgCharWidth = calculateMixedCharWidth(lastLine, fontSize)
    const availableLineWidth = width - padding * 2
    const maxCharsInLastLine = Math.floor(availableLineWidth / avgCharWidth) - 3 // Reserve space for "..."

    if (lastLine.length > maxCharsInLastLine && maxCharsInLastLine > 0) {
      displayLines[displayLines.length - 1] = lastLine.substring(0, maxCharsInLastLine) + "..."
    } else {
      displayLines[displayLines.length - 1] = lastLine + "..."
    }
  }

  // Create SVG for text rendering with better font support
  const svgHeight = height

  let svgContent = `<svg width="${width}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="black"/>
    <style>
      .text {
        font-family: "Arial Unicode MS", Arial, "Helvetica Neue", Helvetica, sans-serif;
        font-size: ${fontSize}px;
        fill: white;
        dominant-baseline: hanging;
        text-rendering: optimizeLegibility;
      }
    </style>`

  // Add text lines with proper spacing
  displayLines.forEach((line: string, index: number) => {
    const y = padding + index * lineHeight
    svgContent += `<text x="${padding}" y="${y}" class="text">${escapeXml(line)}</text>`
  })

  svgContent += "</svg>"

  // Convert SVG to image buffer
  return await sharp(Buffer.from(svgContent)).png().toBuffer()
}

/**
 * Escape XML special characters
 */
function escapeXml(text: string): string {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
}

/**
 * Layout 1: Both images horizontal - Text + Image A + Image B (vertical stack)
 */
export async function layoutBothHorizontal(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine the width based on the higher resolution image
  const maxWidth = Math.max(imageA.width, imageB.width)

  // Scale images to match the max width
  const scaledImageA = await scaleImage(imageA, maxWidth)
  const scaledImageB = await scaleImage(imageB, maxWidth)

  // Get scaled image dimensions
  const metadataA = await sharp(scaledImageA).metadata()
  const metadataB = await sharp(scaledImageB).metadata()

  // Calculate text configuration
  const textConfig = calculateTextConfig(text, maxWidth, maxWidth, Math.max(imageA.height, imageB.height))
  const textImage = await createTextImage(textConfig, "layout1-2")

  // Calculate total height
  const totalHeight = textConfig.height + (metadataA.height || 0) + (metadataB.height || 0)

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledImageA, top: textConfig.height, left: 0 },
    { input: scaledImageB, top: textConfig.height + (metadataA.height || 0), left: 0 },
  ]

  const result = await sharp({
    create: {
      width: maxWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: maxWidth,
    height: totalHeight,
  }
}

/**
 * Layout 2: Both images vertical - Text top, Image A + Image B side-by-side bottom
 */
export async function layoutBothVertical(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine the width based on the higher resolution image
  const maxImageWidth = Math.max(imageA.width, imageB.width)
  const totalWidth = maxImageWidth * 2

  // Scale images to half the total width each
  const targetImageWidth = maxImageWidth
  const scaledImageA = await scaleImage(imageA, targetImageWidth)
  const scaledImageB = await scaleImage(imageB, targetImageWidth)

  // Get scaled image dimensions
  const metadataA = await sharp(scaledImageA).metadata()
  const metadataB = await sharp(scaledImageB).metadata()

  const imageHeight = Math.max(metadataA.height || 0, metadataB.height || 0)

  // Calculate text configuration
  const textConfig = calculateTextConfig(text, totalWidth, totalWidth, Math.max(imageA.height, imageB.height))
  const textImage = await createTextImage(textConfig, "layout1-2")

  // Calculate total height
  const totalHeight = textConfig.height + imageHeight

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledImageA, top: textConfig.height, left: 0 },
    { input: scaledImageB, top: textConfig.height, left: targetImageWidth },
  ]

  const result = await sharp({
    create: {
      width: totalWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: totalWidth,
    height: totalHeight,
  }
}

/**
 * Layout 3: Mixed orientations - Text + vertical image side-by-side top, horizontal image bottom
 */
export async function layoutMixed(
  text: string,
  imageA: ImageInfo,
  imageB: ImageInfo,
): Promise<CompositionResult> {
  // Determine which image is vertical and which is horizontal
  const verticalImage = imageA.isHorizontal ? imageB : imageA
  const horizontalImage = imageA.isHorizontal ? imageA : imageB

  // The vertical image width should be 1/2 the width of the horizontal image
  const horizontalWidth = horizontalImage.width
  const verticalTargetWidth = Math.floor(horizontalWidth / 2)

  // Scale images
  const scaledHorizontal = await scaleImage(horizontalImage, horizontalWidth)
  const scaledVertical = await scaleImage(verticalImage, verticalTargetWidth)

  // Get scaled image dimensions
  const horizontalMetadata = await sharp(scaledHorizontal).metadata()
  const verticalMetadata = await sharp(scaledVertical).metadata()

  const horizontalHeight = horizontalMetadata.height || 0
  const verticalHeight = verticalMetadata.height || 0

  // Calculate text area dimensions (remaining space next to vertical image)
  const textWidth = horizontalWidth - verticalTargetWidth
  const upperSectionHeight = verticalHeight

  // Use the specialized text configuration for Layout 3 (mixed orientations)
  const textConfig = calculateTextConfigForMixed(
    text,
    textWidth,
    upperSectionHeight,
    horizontalWidth,
    horizontalHeight,
  )
  const textImage = await createTextImage(textConfig, "layout3") // Use word wrapping

  // Calculate total dimensions
  const totalWidth = horizontalWidth
  const totalHeight = upperSectionHeight + horizontalHeight

  // Compose the final image
  const compositeOperations = [
    { input: textImage, top: 0, left: 0 },
    { input: scaledVertical, top: 0, left: textWidth },
    { input: scaledHorizontal, top: upperSectionHeight, left: 0 },
  ]

  const result = await sharp({
    create: {
      width: totalWidth,
      height: totalHeight,
      channels: 3,
      background: { r: 0, g: 0, b: 0 },
    },
  })
    .composite(compositeOperations)
    .png()
    .toBuffer()

  return {
    buffer: result,
    width: totalWidth,
    height: totalHeight,
  }
}

/**
 * Validate text input
 */
function validateText(text: string): void {
  if (!text || typeof text !== "string") {
    throw new Error("Text must be a non-empty string")
  }

  if (text.length > 5000) {
    throw new Error("Text too long. Maximum length: 5000 characters")
  }

  // Remove excessive whitespace but preserve intentional spaces
  const cleanedText = text.trim()
  if (!cleanedText) {
    throw new Error("Text cannot be empty or only whitespace")
  }
}

/**
 * Main function to compose text and two images according to layout rules
 */
export async function composeImages(
  text: string,
  imageUrlA: string,
  imageUrlB: string,
): Promise<CompositionResult> {
  // Validate inputs
  validateText(text)

  if (!imageUrlA || !imageUrlB) {
    throw new Error("Both image URLs must be provided")
  }

  if (imageUrlA === imageUrlB) {
    throw new Error("Image URLs must be different")
  }

  try {
    // Fetch both images
    const [imageA, imageB] = await Promise.all([fetchImageInfo(imageUrlA), fetchImageInfo(imageUrlB)])

    // Determine layout type
    const layoutType = determineLayoutType(imageA, imageB)

    // Apply appropriate layout
    switch (layoutType) {
      case "both-horizontal":
        return await layoutBothHorizontal(text, imageA, imageB)
      case "both-vertical":
        return await layoutBothVertical(text, imageA, imageB)
      case "mixed":
        return await layoutMixed(text, imageA, imageB)
      default:
        throw new Error(`Unknown layout type: ${layoutType}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Image composition failed: ${String(error)}`)
  }
}
