import { LRUCache } from "lru-cache"
import { cachified, <PERSON>acheEntry, Cache, totalTtl } from "@epic-web/cachified"
import { LoaderFunction } from "@remix-run/node"

const lruInstance = new LRUCache<string, CacheEntry>({
  max: 5000, // Increased from 1000
  maxSize: 500 * 1024 * 1024, // 500MB max memory
  ttl: 1000 * 60 * 30, // 30 minutes default TTL
})

const lru: Cache = {
  set(key, value) {
    const ttl = totalTtl(value?.metadata)
    return lruInstance.set(key, value, {
      ttl: ttl === Infinity ? undefined : ttl,
      start: value?.metadata?.createdTime,
    })
  },
  get(key) {
    return lruInstance.get(key)
  },
  delete(key) {
    return lruInstance.delete(key)
  },
}

// Cache statistics for monitoring
export const getCacheStats = () => ({
  size: lruInstance.size,
  calculatedSize: lruInstance.calculatedSize,
  max: lruInstance.max,
  maxSize: lruInstance.maxSize,
  hitRate: lruInstance.size > 0 ? lruInstance.size / (lruInstance.size + 1) : 0,
})

const cachifiedGroups = new Map<string, string[]>()

const byCachified = (
  group: string,
  key: string,
  handler: LoaderFunction,
  args: Parameters<LoaderFunction>[0],
  forceFresh = false,
) => {
  if (cachifiedGroups.has(group)) {
    const keys = cachifiedGroups.get(group)!
    if (!keys.includes(key)) {
      keys?.push(key)
    }
  } else {
    cachifiedGroups.set(group, [key])
  }

  return cachified({
    key,
    cache: lru,
    getFreshValue: async (context) => {
      const resp = await handler(args)
      if (
        resp instanceof Response &&
        resp.ok &&
        resp.body !== null &&
        resp.headers.get("Content-Type")?.includes("application/json")
      ) {
        console.log("cache fresh", key)
        return await resp.json()
      }
      throw resp
    },
    forceFresh,
    ttl: 600_000, // 10 minutes
    staleWhileRevalidate: 20_000,
    staleRefreshTimeout: 10_000,
    fallbackToCache: false,
  })
}

export default byCachified

export const cleanCachified = (...groups: string[]) => {
  for (const group of groups) {
    if (!cachifiedGroups.has(group)) continue

    for (const key of cachifiedGroups.get(group)!) {
      lru.delete(key)
    }
  }
}
