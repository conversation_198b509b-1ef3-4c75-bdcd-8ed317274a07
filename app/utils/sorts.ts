// import { QueryValues } from "~/models/values.query.server"
// import { HttpStatusCode as Status } from "~/utils/http_code"

// export enum OrderScending {
//   ASC = "asc",
//   DESC = "desc",
// }

// export const sort_query_values = (list: QueryValues[], orders_by: string[], orders: OrderScending[]) => {
//   if (!orders_by.length) return

//   if (orders_by.length !== orders.length) {
//     throw new Response("order_by,order conflict", { status: Status.BadRequest })
//   }

//   list.sort((a, b) => {
//     for (let i = 0; i < orders_by.length; i++) {
//       const key = orders_by[i]
//       const order = orders[i]

//       const avalue = a.values.find((v) => v.slug === key)?.value[0]
//       const bvalue = b.values.find((v) => v.slug === key)?.value[0]

//       if (avalue == undefined || bvalue == undefined || avalue === bvalue) {
//         continue
//       }

//       if (order === OrderScending.ASC) {
//         return avalue > bvalue ? 1 : -1
//       } else if (order === OrderScending.DESC) {
//         return avalue < bvalue ? 1 : -1
//       }
//     }

//     return 0
//   })
// }
