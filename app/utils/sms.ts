import dayjs from "dayjs"
import iconv from "iconv-lite"

const send = async (templateld: string, content: string, phone: string): Promise<number> => {
  // console.log("GBKUrlEncode", GBKUrlEncode(content))
  const MessageContent = GBKUrlEncode(content)

  const params = {
    SpCode: "270974",
    LoginName: "cz_jtzf",
    Password: "9f79fe2dab02effad413d32d5ae7b987",
    MessageContent: content,
    UserNumber: phone,
    templateld,
    SerialNumber: dayjs().format("YYYYMMDDHHmmssSSS"),
  }

  const encodedParams = Object.entries(params)
    .map(([key, value]) => {
      const encodedValue = key === "MessageContent" ? MessageContent : encodeURIComponent(value)
      return `${encodeURIComponent(key)}=${encodedValue}`
    })
    .join("&")

  const headers = new Headers()
  headers.append("content-type", "application/x-www-form-urlencoded")
  headers.append("content-length", Buffer.byteLength(encodedParams).toString())

  const response = await fetch("https://api.ums86.com:9600/sms/Api/Send.do", {
    method: "POST",
    headers,
    body: encodedParams,
  })

  if (!response.ok) {
    console.warn("sms send fail", phone, response.status, MessageContent)
    return 99
  }

  const result = await response.text()
  const resultKV = result.split("&")[0]
  const codeStr = resultKV.split("=")[1]
  const code = parseInt(codeStr)
  console.log("sms send result", phone, code, MessageContent)

  return code
}

function GBKUrlEncode(content: string) {
  const gbkBuffer = iconv.encode(content, "gbk")

  let result = ""
  for (const byte of gbkBuffer) {
    const char = String.fromCharCode(byte)
    // if the character is an unreserved character
    if (/^[A-Za-z0-9\-_.~]$/.test(char)) {
      result += char
    } else {
      result += "%" + byte.toString(16).toUpperCase().padStart(2, "0")
    }
  }
  return result
}

export default send
