import sharp from "sharp"
import { existsSync, mkdirSync } from "fs"
import { HttpStatusCode as Status } from "~/utils/http_code"

interface RoboflowDetection {
  image: {
    width: number
    height: number
  }
  predictions: Predictions[]
}

interface Predictions {
  x: number
  y: number
  width: number
  height: number
  confidence: number
  class: string
}

const API_KEY = "LJrkZIiy6lhgcpv3k5EQ"
const roboFlowApi = (model: string) => `${process.env.ROBOFLOW_HOST}/${model}?api_key=${API_KEY}`

export const detectCodes = async (imageBase64: string, model: string): Promise<RoboflowDetection> => {
  const response = await fetch(roboFlowApi(model), {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: imageBase64,
  })

  if (!response.ok) {
    throw new Response(`${model} detection fail ${await response.text()}`, {
      status: Status.InternalServerError,
    })
  }

  return response.json()
}

const clampedRegion = (diction: Predictions, imageWidth: number, imageHeight: number) => {
  const left = Math.max(0, Math.round(diction.x - diction.width / 2))
  const top = Math.max(0, Math.round(diction.y - diction.height / 2))
  const width = Math.min(Math.round(diction.width), imageWidth - left)
  const height = Math.min(Math.round(diction.height), imageHeight - top)

  return { left, top, width, height }
}

export async function extractCodeBuffers(
  imageBuffer: Buffer,
  detection: RoboflowDetection,
): Promise<(Buffer<ArrayBufferLike> | null)[]> {
  const { width, height } = detection.image
  const image = await sharp(imageBuffer).rotate()

  // const outputDir = "./public/extracted_regions"
  // if (!existsSync(outputDir)) {
  //   mkdirSync(outputDir, { recursive: true })
  // }

  return await Promise.all(
    detection.predictions.map(async (pd) => {
      const region = clampedRegion(pd, width, height)

      if (region.width <= 0 || region.height <= 0) {
        console.warn("extractCodeBuffers invalid prediction", pd)
        return null
      }

      try {
        const region_image = await image.clone().extract(region)

        // await region_image
        //   .jpeg()
        //   .toFile(`${outputDir}/region_${Date.now()}_${pd.width}_${pd.height}_${pd.x}_${pd.y}.jpg`)

        return region_image.toBuffer()
      } catch (error) {
        console.error(`extractCodeBuffers extracting error. ${JSON.stringify({ pd, region })}`, error)
        return null
      }
    }),
  )
}
