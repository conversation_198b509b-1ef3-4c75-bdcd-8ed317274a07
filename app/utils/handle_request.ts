import { ActionFunction, LoaderFunction } from "@remix-run/node"
import dayjs from "dayjs"
import utc from "dayjs/plugin/utc"
import { drizzle } from "drizzle-orm/node-postgres"
import { eq, and } from "drizzle-orm"

import * as users_schema from "~/schema/users"
import { isAuthenticated } from "~/auth.form.server"
import { HttpMethod as Method, HttpStatusCode as Status } from "~/utils/http_code"
import byCachified from "./cache"

dayjs.extend(utc)

type DataFunctionValue = Response | NonNullable<unknown> | null
type HandleArgs = {
  auth: boolean
  cachified?: string
}

const { users } = users_schema
const db = drizzle(process.env.DATABASE_URL!, {
  schema: { ...users_schema },
})

export default function HandleRequest<T extends LoaderFunction | ActionFunction>(
  handler: T,
  { auth, cachified }: HandleArgs = { auth: true },
): DataFunctionValue {
  return async function (args: Parameters<T>[0]) {
    const { request } = args

    if (auth) {
      const account = await isAuthenticated(request)
      args.context.account = account

      const users_ = await db.query.users.findMany({
        where: and(eq(users.account_id, account.id), eq(users.using, true)),
      })
      if (users_.length === 0) throw new Response("user absent", { status: Status.InternalServerError })
      args.context.user = users_[0]
    }

    let resp: DataFunctionValue
    if (cachified !== undefined && request.method === Method.GET) {
      try {
        const body = await byCachified(cachified, request.url, handler, args)
        resp = Response.json(body)
      } catch (resp_) {
        resp = resp_ as DataFunctionValue
        console.warn("cache fallback", request.url)
      }
    } else {
      resp = await handler(args)
    }

    if (resp instanceof Response) {
      resp.headers.set("Access-Control-Allow-Origin", "*")
      resp.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, DELETE")

      const content_type = resp.headers.get("Content-Type")
      if (resp.ok && resp.body !== null && content_type?.includes("application/json")) {
        const body = await resp.clone().json()
        if (format_iso_date(body)) {
          return new Response(JSON.stringify(body), {
            status: resp.status,
            statusText: resp.statusText,
            headers: resp.headers,
          })
        }
      }
    }

    return resp
  }
}

// https://github.com/drizzle-team/drizzle-orm/issues/1757
const format_iso_date = (obj: any, mutated = false): boolean => {
  if (Array.isArray(obj)) {
    for (const item of obj) {
      mutated = format_iso_date(item, mutated) || mutated
    }
    return mutated
  }

  if (typeof obj !== "object" || obj === null) {
    return mutated
  }

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      if (typeof value === "string" && key.toLowerCase().endsWith("time")) {
        obj[key] = dayjs.utc(value).toISOString()
        mutated = true
      }
      // https://github.com/drizzle-team/drizzle-orm/issues/1757#issuecomment-2585715742
      else if (typeof value === "object" || Array.isArray(value)) {
        mutated = format_iso_date(value, mutated) || mutated
      }
    }
  }

  return mutated
}
