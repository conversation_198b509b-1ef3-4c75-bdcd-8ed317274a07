import { HttpStatusCode as Status } from "./http_code"
import type { ZodSchema } from "zod"

export enum OrderScending {
  ASC = "asc",
  DESC = "desc",
}

export interface Order {
  readonly column: string
  readonly scending: OrderScending
}

export class URLSearchParamsExtended extends URLSearchParams {
  constructor(url: string) {
    super(new URL(url).search)
  }

  require_string(key: string): string {
    const raw = this.get(key)
    if (!raw) {
      throw new Response(`${key} missing`, { status: Status.BadRequest })
    }
    return raw
  }

  parse_boolean(key: string, require = false): boolean | undefined {
    const raw = this.get(key)
    if (require && raw == null) {
      throw new Response(`${key} missing`, { status: Status.BadRequest })
    }
    return raw == null ? undefined : raw === "true"
  }

  parse_integer<R extends boolean>(
    key: string,
    require = false as R,
  ): R extends true ? number : number | undefined {
    const raw = this.get(key)
    const value = raw ? parseInt(raw, 10) : NaN
    if (require && isNaN(value)) {
      throw new Response(`${key} missing`, { status: Status.BadRequest })
    }
    return (isNaN(value) ? undefined : value) as R extends true ? number : number | undefined
  }

  parse_integer_array(key: string): number[] {
    const raw = this.get(key)
    if (!raw) return []

    return raw.split(",").map((item) => {
      const value = parseInt(item, 10)
      if (isNaN(value)) {
        throw new Response(`${key} invalid`, { status: Status.BadRequest })
      }
      return value
    })
  }

  parse_zod<T, R extends boolean>(
    key: string,
    schema: ZodSchema<T>,
    require = false as R,
  ): R extends true ? T : T | undefined {
    const raw = this.get(key)
    if (!raw) {
      if (require) throw new Response(`${key} missing`, { status: Status.BadRequest })
      else return undefined as R extends true ? T : T | undefined
    }

    const { success, data } = schema.safeParse(raw)
    if (!success) {
      throw new Response(`${key} invalid`, { status: Status.BadRequest })
    }
    return data
  }

  parse_zods<T, R extends boolean>(
    key: string,
    schema: ZodSchema<T>,
    require: boolean = false,
  ): R extends true ? T[] : T[] | undefined {
    const raw = this.get(key)
    if (!raw) {
      if (require) throw new Response(`${key} missing`, { status: Status.BadRequest })
      else return undefined as R extends true ? T[] : T[] | undefined
    }

    return raw.split(",").map((item) => {
      const { success, data } = schema.safeParse(item)
      if (!success) {
        throw new Response(`${key} invalid`, { status: Status.BadRequest })
      }
      return data
    })
  }

  parse_orders(): Order[] {
    const orders = this.get("orders")
    if (!orders) return []

    return orders.split(",").map((orderString) => {
      const [column, scending] = orderString.split(".")

      if (!Object.values(OrderScending).includes(scending as OrderScending)) {
        throw new Response(`Invalid sort direction: ${scending}`, { status: Status.BadRequest })
      }

      return { column, scending: scending as OrderScending } as const
    })
  }
}
