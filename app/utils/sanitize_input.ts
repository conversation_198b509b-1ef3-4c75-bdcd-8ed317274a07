// SQL 危险关键字正则表达式
const SQL_KEYWORDS_REGEX = /\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE|SELECT)\b/gi
const SQL_COMMENT_REGEX = /--/g
const DANGEROUS_CHARS_REGEX = /[';-]/g
const LIKE_SPECIAL_CHARS_REGEX = /[%_\\]/g

/**
 * 清理和验证字符串输入，防止 SQL 注入
 * @param input 要清理的输入字符串
 * @returns 清理后的安全字符串
 */
export const sanitizeString = (input: string): string => {
  if (!input) return input

  return input
    .replace(DANGEROUS_CHARS_REGEX, "") // 移除分号和连字符
    .replace(SQL_COMMENT_REGEX, "") // 移除 SQL 注释
    .replace(SQL_KEYWORDS_REGEX, "") // 移除危险的 SQL 关键字
    .trim()
}

/**
 * 验证和清理 LIKE 查询的输入，转义特殊字符
 * @param input 要清理的 LIKE 查询输入
 * @returns 清理并转义后的安全字符串
 */
export const sanitizeLike = (input: string): string => {
  if (!input) return input

  return input
    .replace(LIKE_SPECIAL_CHARS_REGEX, "\\$&") // 转义 %, _, \ 字符
    .replace(DANGEROUS_CHARS_REGEX, "") // 移除分号和连字符
    .replace(SQL_COMMENT_REGEX, "") // 移除 SQL 注释
    .replace(SQL_KEYWORDS_REGEX, "") // 移除危险的 SQL 关键字
    .trim()
}
