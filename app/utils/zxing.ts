import sharp from "sharp"
import fs from "fs"
import {
  MultiFormatReader,
  RGBLuminanceSource,
  BinaryBitmap,
  HybridBinarizer,
  BarcodeFormat,
  DecodeHintType,
} from "@zxing/library"

const zxingReader = new MultiFormatReader()

const hints = new Map()
const formats = Object.values(BarcodeFormat).filter((value) => typeof value === "number") as BarcodeFormat[]
hints.set(DecodeHintType.POSSIBLE_FORMATS, formats)
hints.set(DecodeHintType.TRY_HARDER, true)

export async function decodeImage(imageBuffer: Buffer<ArrayBufferLike> | null): Promise<string | null> {
  if (!imageBuffer) return null

  const image = await sharp(imageBuffer)
  const metadata = await image.metadata()
  console.log(`decodeImage ${metadata.width},${metadata.height},${metadata.channels}`)
  // image.toFile(`public/decode-${metadata.width}-${metadata.height}.jpeg`)

  const rawDataBuffer = await image.ensureAlpha(1).raw().toBuffer()
  // await sharp(rawDataBuffer, {
  //   raw: {
  //     width: metadata.width,
  //     height: metadata.height,
  //     channels: 4,
  //   },
  // })
  //   .jpeg()
  //   .toFile(`public/decode-raw-${metadata.width}-${metadata.height}.jpeg`)

  try {
    const rgbaPixels = new Uint8ClampedArray(rawDataBuffer)
    const grayscalePixels = new Uint8ClampedArray(metadata.width * metadata.height)
    for (let i = 0; i < metadata.width * metadata.height; i++) {
      const r = rgbaPixels[i * 4] // Red component
      const g = rgbaPixels[i * 4 + 1] // Green component
      const b = rgbaPixels[i * 4 + 2] // Blue component
      // Convert RGB to luminance using the same green-favouring average as ZXing's Int32Array path
      // (R + 2G + B) / 4
      grayscalePixels[i] = ((r + (g << 1) + b) / 4) & 0xff
    }

    const luminanceSource = new RGBLuminanceSource(grayscalePixels, metadata.width, metadata.height)
    const binaryBitmap = new BinaryBitmap(new HybridBinarizer(luminanceSource))
    // await saveBinaryBitmapAsImage(
    //   binaryBitmap,
    //   `public/decode-bitmap-${metadata.width}-${metadata.height}.jpeg`,
    // )

    const result = zxingReader.decode(binaryBitmap, hints)
    const text = result.getText()

    console.log(`decodeImage ${text}`)
    return text
  } catch (error) {
    console.error(`decodeImage error ${metadata.width},${metadata.height}`, error)
    return `error: ${error}`
  }
}

async function saveBinaryBitmapAsImage(binaryBitmap: BinaryBitmap, outputPath: string): Promise<void> {
  try {
    const width = binaryBitmap.getWidth()
    const height = binaryBitmap.getHeight()
    const blackMatrix = binaryBitmap.getBlackMatrix()

    const outputImageBuffer = Buffer.alloc(width * height * 3, 255)

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        if (blackMatrix.get(x, y)) {
          const offset = (y * width + x) * 3
          outputImageBuffer[offset] = 0 // R = 0 (black)
          outputImageBuffer[offset + 1] = 0 // G = 0 (black)
          outputImageBuffer[offset + 2] = 0 // B = 0 (black)
        }
      }
    }

    const outputDir = outputPath.substring(0, outputPath.lastIndexOf("/"))
    if (outputDir && !fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    await sharp(outputImageBuffer, {
      raw: {
        width: width,
        height: height,
        channels: 3,
      },
    })
      .jpeg()
      .toFile(outputPath)

    console.log(`BinaryBitmap saved to ${outputPath}`)
  } catch (error) {
    console.error("Error saving BinaryBitmap as image:", error)
  }
}
