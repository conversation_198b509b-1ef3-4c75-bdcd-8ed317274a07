import { eq, sql, S<PERSON><PERSON><PERSON><PERSON> } from "drizzle-orm"

export const eq_optional_point_types = (column: SQLWrapper, placeholder: string) =>
  sql`(${sql.placeholder(placeholder)}::point_types IS NULL OR ${column} = ${sql.placeholder(placeholder)}::point_types)`

export const eq_optional_text = (column: SQLWrapper, placeholder: string) =>
  sql`(${sql.placeholder(placeholder)}::text IS NULL OR ${column} = ${sql.placeholder(placeholder)}::text)`

export const eq_optional_integer = (column: SQLWrapper, placeholder: string) =>
  sql`(${sql.placeholder(placeholder)}::integer IS NULL OR ${column} = ${sql.placeholder(placeholder)}::integer)`

export const eq_optional_boolean = (column: SQLWrapper, placeholder: string) =>
  sql`(${sql.placeholder(placeholder)}::boolean IS NULL OR ${column} = ${sql.placeholder(placeholder)}::boolean)`

export const in_integer_array = (column: <PERSON><PERSON><PERSON><PERSON><PERSON>, placeholder: string) => {
  // https://github.com/drizzle-team/drizzle-orm/issues/1415#issuecomment-1858969552
  // inArray(column, sql`${sql.placeholder(placeholder)}`),
  return eq(column, sql`any(string_to_array(${sql.placeholder(placeholder)}, ',')::integer[])`)
}
