{"mcp": {"servers": {"postgresql": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "${input:pg_url}"]}}, "inputs": [{"type": "promptString", "id": "pg_url", "description": "PostgreSQL URL", "default": "postgresql://username:password@host:port/database"}]}, "cSpell.words": ["allocats", "alicloud", "cachified", "checkin", "checkins", "ilike", "libsql", "zxing", "msgpack", "msgpackr"], "typescript.experimental.useTsgo": false}