import { createRe<PERSON><PERSON><PERSON><PERSON> } from "@remix-run/express"
import compression from "compression"
import express from "express"
import morgan from "morgan"
import zlib from "zlib"
import msgpack from "express-msgpack"
import { unpack, pack } from "msgpackr"

const viteDevServer =
  process.env.NODE_ENV === "production"
    ? undefined
    : await import("vite").then((vite) =>
        vite.createServer({
          server: { middlewareMode: true },
        }),
      )

const remixHandler = createRequestHandler({
  build: viteDevServer
    ? () => viteDevServer.ssrLoadModule("virtual:remix/server-build")
    : await import("./build/server/index.js"),
})

const app = express()

if (process.env.NODE_ENV === "production") {
  app.set("trust proxy", 1) // Trust first proxy
  app.set("x-powered-by", false) // Already disabled below, but explicit
}

// Security and performance headers middleware
app.use((req, res, next) => {
  // Security headers
  res.setHeader("X-Content-Type-Options", "nosniff")
  res.setHeader("X-Frame-Options", "DENY")
  res.setHeader("X-XSS-Protection", "1; mode=block")
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin")

  // Performance headers
  res.setHeader("X-DNS-Prefetch-Control", "on")

  next()
})

const brotliOptions = {
  level: zlib.constants.BROTLI_PARAM_QUALITY,
  params: {
    [zlib.constants.BROTLI_PARAM_QUALITY]: process.env.NODE_ENV === "production" ? 6 : 4,
    [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,
  },
}

app.use(
  compression({
    filter: (req, res) => {
      if (req.headers["x-no-compression"]) {
        return false
      }

      // https://www.npmjs.com/package/compressible
      return compression.filter(req, res)
    },
    brotli: brotliOptions,
    threshold: 1024, // Only compress responses larger than 1KB
    level: 6,
  }),
)

app.use(
  msgpack({
    decoder: unpack,
    encoder: pack,
    limit: "50mb",
    type: "application/msgpack",
  }),
)

// http://expressjs.com/en/advanced/best-practice-security.html#at-a-minimum-disable-x-powered-by-header
app.disable("x-powered-by")

// handle asset requests
if (viteDevServer) {
  app.use(viteDevServer.middlewares)
} else {
  // Vite fingerprints its assets so we can cache forever.
  app.use(
    "/assets",
    express.static("build/client/assets", {
      immutable: true,
      maxAge: "1y",
      etag: true,
      lastModified: true,
    }),
  )

  // Optimized caching for Everything else (like favicon.ico)
  app.use(
    express.static("build/client", {
      maxAge: "1h",
      etag: true,
      lastModified: true,
    }),
  )
}

app.use(morgan("tiny"))

// handle SSR requests
app.all("*", remixHandler)

const port = process.env.PORT || 5173
app.listen(port, () => console.log(`server listening at http://localhost:${port} 0401.0`))
