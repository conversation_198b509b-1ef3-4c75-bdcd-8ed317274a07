module.exports = {
  apps: [
    {
      name: "forms4",
      // interpreter: "bash",
      script: "bun",
      args: ["start"],
      autorestart: true,
      env: {
        NODE_ENV: "production",
        PORT: 18080,
        HOST: "0.0.0.0",
      },
    },
  ],

  deploy: {
    production: {
      user: "czmp",
      host: [
        {
          host: "**************",
          port: "50022",
        },
      ],
      ref: "origin/profiles",
      repo: "*********************:czmp/changzhou-maipu/procedure-forms.git",
      path: "/home/<USER>/forms4",
      "post-deploy": "pnpm install && bun run build && pm2 reload ecosystem.config.cjs",
    },
  },
}

// after `pm2 deploy production setup`, vi .env in source folder, then npm build will bring the .env
