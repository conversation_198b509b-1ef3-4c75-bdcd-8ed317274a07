import "dotenv/config"
import { defineConfig } from "drizzle-kit"

// export default defineConfig({
//   out: "./drizzle",
//   schema: "./app/db/schema.server.sqlite.ts",
//   dialect: "sqlite",
//   dbCredentials: {
//     url: process.env.DB_FILE_NAME!,
//   },
// });

export default defineConfig({
  out: "./drizzle",
  schema: [
    "./app/schema/accounts.ts",
    "./app/schema/users.ts",
    "./app/schema/projects.ts",
    "./app/schema/points.ts",
    "./app/schema/point_types.ts",
    "./app/schema/forms.ts",
    "./app/schema/units.ts",
    "./app/schema/submissions.ts",
    "./app/schema/issues.ts",
    "./app/schema/issue_replies.ts",
    "./app/schema/materials.ts",
    "./app/schema/material_allocats.ts",
    "./app/schema/configs.ts",
    "./app/schema/config_cates.ts",
    "./app/schema/cabinets.ts",
    "./app/schema/cabinet_materials.ts",
    "./app/schema/ports.ts",
    "./app/schema/orders.ts",
    "./app/schema/order_replies.ts",
    "./app/schema/links.ts",
    "./app/schema/point_logs.ts",
    "./app/schema/submission_logs.ts",
    "./app/schema/checkins.ts",
    "./app/schema/checkin_devices.ts",
    "./app/schema/pickings.ts",
    "./app/schema/picking_items.ts",
  ],
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
})
