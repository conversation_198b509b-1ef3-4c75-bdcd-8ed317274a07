{"id": "0c9c39b2-7e4b-4192-8670-9a9465a16316", "prevId": "3330500b-5f62-46e0-8305-d31160bfbf0c", "version": "7", "dialect": "postgresql", "tables": {"public.form_units": {"name": "form_units", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "form_units_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "unit_types", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'text'"}, "index": {"name": "index", "type": "integer", "primaryKey": false, "notNull": false}, "props": {"name": "props", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}}, "indexes": {"units_slug_idx": {"name": "units_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"form_units_form_id_forms_id_fk": {"name": "form_units_form_id_forms_id_fk", "tableFrom": "form_units", "tableTo": "forms", "columnsFrom": ["form_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms": {"name": "forms", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "forms_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "point_types", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'backend'"}, "index": {"name": "index", "type": "integer", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {"forms_slug_idx": {"name": "forms_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"forms_slug_unique": {"name": "forms_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.points": {"name": "points", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "points_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "manager_id": {"name": "manager_id", "type": "integer", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "point_types", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'backend'"}}, "indexes": {"points_slug_idx": {"name": "points_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"points_manager_id_users_id_fk": {"name": "points_manager_id_users_id_fk", "tableFrom": "points", "tableTo": "users", "columnsFrom": ["manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "points_project_id_projects_id_fk": {"name": "points_project_id_projects_id_fk", "tableFrom": "points", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "projects_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "users": {"name": "users", "type": "var<PERSON>r[]", "primaryKey": false, "notNull": true, "default": "ARRAY[]::text[]"}}, "indexes": {"projects_slug_idx": {"name": "projects_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"projects_slug_unique": {"name": "projects_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.submit": {"name": "submit", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "submit_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "time": {"name": "time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "point_id": {"name": "point_id", "type": "integer", "primaryKey": false, "notNull": true}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"submit_point_id_points_id_fk": {"name": "submit_point_id_points_id_fk", "tableFrom": "submit", "tableTo": "points", "columnsFrom": ["point_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "submit_form_id_forms_id_fk": {"name": "submit_form_id_forms_id_fk", "tableFrom": "submit", "tableTo": "forms", "columnsFrom": ["form_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "submit_user_id_users_id_fk": {"name": "submit_user_id_users_id_fk", "tableFrom": "submit", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.submit_data": {"name": "submit_data", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "submit_data_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "type": {"name": "type", "type": "unit_types", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'text'"}, "submit_id": {"name": "submit_id", "type": "integer", "primaryKey": false, "notNull": true}, "unit_id": {"name": "unit_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "var<PERSON>r[]", "primaryKey": false, "notNull": true, "default": "ARRAY[]::text[]"}}, "indexes": {}, "foreignKeys": {"submit_data_submit_id_submit_id_fk": {"name": "submit_data_submit_id_submit_id_fk", "tableFrom": "submit_data", "tableTo": "submit", "columnsFrom": ["submit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "submit_data_unit_id_form_units_id_fk": {"name": "submit_data_unit_id_form_units_id_fk", "tableFrom": "submit_data", "tableTo": "form_units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "users_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "roles", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'user'"}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "name_chinese": {"name": "name_chinese", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_name_unique": {"name": "users_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}, "users_name_chinese_unique": {"name": "users_name_chinese_unique", "nullsNotDistinct": false, "columns": ["name_chinese"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.point_types": {"name": "point_types", "schema": "public", "values": ["frontend", "backend", "converge", "connect"]}, "public.roles": {"name": "roles", "schema": "public", "values": ["user", "manager", "admin"]}, "public.unit_types": {"name": "unit_types", "schema": "public", "values": ["text", "number", "range", "date", "time", "datetime", "phone", "email", "url", "select", "select-multiple", "file", "file-multiple", "form-related", "dividing"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}