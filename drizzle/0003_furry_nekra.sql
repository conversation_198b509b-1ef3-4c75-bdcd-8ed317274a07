ALTER TABLE "points" RENAME COLUMN "manager_id" TO "parent_id";--> statement-breakpoint
ALTER TABLE "forms" DROP CONSTRAINT "forms_slug_unique";--> statement-breakpoint
ALTER TABLE "points" DROP CONSTRAINT "points_manager_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "form_units" ALTER COLUMN "slug" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "form_units" ALTER COLUMN "type" SET DATA TYPE varchar(64);--> statement-breakpoint
ALTER TABLE "form_units" ALTER COLUMN "type" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "form_units" ALTER COLUMN "type" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "forms" ALTER COLUMN "slug" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "submit_data" ALTER COLUMN "type" SET DATA TYPE varchar(64);--> statement-breakpoint
ALTER TABLE "submit_data" ALTER COLUMN "type" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "submit_data" ALTER COLUMN "type" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "form_units" ADD COLUMN "require" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "forms" ADD COLUMN "project_id" integer;--> statement-breakpoint
ALTER TABLE "projects" ADD COLUMN "manager_id" integer;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "disable" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "forms" ADD CONSTRAINT "forms_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points" ADD CONSTRAINT "points_parent_id_points_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."points"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "projects" ADD CONSTRAINT "projects_manager_id_users_id_fk" FOREIGN KEY ("manager_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "projects" ADD CONSTRAINT "projects_name_unique" UNIQUE("name");--> statement-breakpoint
DROP TYPE "public"."unit_types";