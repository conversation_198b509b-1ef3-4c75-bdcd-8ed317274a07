CREATE TYPE "public"."point_types" AS ENUM('frontend', 'backend', 'converge', 'connect');--> statement-breakpoint
CREATE TYPE "public"."roles" AS ENUM('user', 'manager', 'admin');--> statement-breakpoint
CREATE TYPE "public"."unit_types" AS ENUM('text', 'number', 'range', 'date', 'time', 'datetime', 'phone', 'email', 'url', 'select', 'select-multiple', 'file', 'file-multiple', 'form-related', 'dividing');--> statement-breakpoint
CREATE TABLE "form_units" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "form_units_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"slug" varchar(256),
	"form_id" integer NOT NULL,
	"label" varchar(256),
	"type" "unit_types" DEFAULT 'text',
	"index" integer,
	"props" json DEFAULT '{}'::json
);
--> statement-breakpoint
CREATE TABLE "forms" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "forms_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"title" varchar(256),
	"slug" varchar(256),
	"type" "point_types" DEFAULT 'backend' NOT NULL,
	"index" integer,
	"icon" varchar,
	CONSTRAINT "forms_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "points" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "points_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"slug" varchar(256),
	"title" varchar(256),
	"manager_id" integer,
	"project_id" integer,
	"type" "point_types" DEFAULT 'backend' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "projects" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "projects_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"slug" varchar(256),
	"name" varchar(256),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"users" varchar[] DEFAULT ARRAY[]::text[] NOT NULL,
	CONSTRAINT "projects_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "submit" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "submit_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"time" timestamp DEFAULT now() NOT NULL,
	"point_id" integer NOT NULL,
	"form_id" integer NOT NULL,
	"user_id" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "submit_data" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "submit_data_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"type" "unit_types" DEFAULT 'text',
	"submit_id" integer NOT NULL,
	"unit_id" integer NOT NULL,
	"value" varchar[] DEFAULT ARRAY[]::text[] NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "users_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"name" varchar(64) NOT NULL,
	"password" varchar(64) NOT NULL,
	"role" "roles" DEFAULT 'user',
	"phone" varchar(64),
	"name_chinese" varchar(64),
	CONSTRAINT "users_name_unique" UNIQUE("name"),
	CONSTRAINT "users_phone_unique" UNIQUE("phone"),
	CONSTRAINT "users_name_chinese_unique" UNIQUE("name_chinese")
);
--> statement-breakpoint
ALTER TABLE "form_units" ADD CONSTRAINT "form_units_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points" ADD CONSTRAINT "points_manager_id_users_id_fk" FOREIGN KEY ("manager_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points" ADD CONSTRAINT "points_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "submit" ADD CONSTRAINT "submit_point_id_points_id_fk" FOREIGN KEY ("point_id") REFERENCES "public"."points"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "submit" ADD CONSTRAINT "submit_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "submit" ADD CONSTRAINT "submit_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "submit_data" ADD CONSTRAINT "submit_data_submit_id_submit_id_fk" FOREIGN KEY ("submit_id") REFERENCES "public"."submit"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "submit_data" ADD CONSTRAINT "submit_data_unit_id_form_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."form_units"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "units_slug_idx" ON "form_units" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "forms_slug_idx" ON "forms" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "points_slug_idx" ON "points" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "projects_slug_idx" ON "projects" USING btree ("slug");