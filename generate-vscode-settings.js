#!/usr/bin/env node

/**
 * Script to generate .vscode/settings.json from environment variables
 * Usage: node generate-vscode-settings.js
 */

import fs from "fs"
import path from "path"
import { fileURLToPath } from "url"
import dotenv from "dotenv"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, "..", ".env") })

const settingsTemplate = {
  mcp: {
    servers: {
      postgresql: {
        command: "npx",
        args: ["-y", "@modelcontextprotocol/server-postgres", "${input:pg_url}"],
      },
    },
    inputs: [
      {
        type: "promptString",
        id: "pg_url",
        description: "PostgreSQL URL",
        default: process.env.DATABASE_URL || "",
      },
    ],
  },
  "cSpell.words": [
    "allocats",
    "alicloud",
    "cachified",
    "checkin",
    "checkins",
    "ilike",
    "libsql",
    "zxing",
    "msgpack",
    "msgpackr",
  ],
}

const settingsPath = path.join(__dirname, ".vscode", "settings.json")

// Ensure .vscode directory exists
const vscodeDirPath = path.dirname(settingsPath)
if (!fs.existsSync(vscodeDirPath)) {
  fs.mkdirSync(vscodeDirPath, { recursive: true })
}

// Write settings.json
fs.writeFileSync(settingsPath, JSON.stringify(settingsTemplate, null, 2))

console.log("✅ Generated .vscode/settings.json with environment variables")
